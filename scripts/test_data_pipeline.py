#!/usr/bin/env python3
"""
Test script to validate the entire data processing pipeline.
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Any

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from langextract_core.models import (
    ParsedResult, ExtractionResultDocument, PromptTemplate
)
from langextract_core.extractor import ProductExtractor
from langextract_core.storage import MongoStorage


def test_parsed_result_validation():
    """Test ParsedResult model validation."""
    print("🔍 Testing ParsedResult validation...")
    
    # Test valid data
    try:
        result = ParsedResult(
            BaseProductName="Test Product",
            Option1Name="Size",
            Option1Value="Large",
            Option2Name="Color",
            Option2Value="Blue"
        )
        print("✅ Valid ParsedResult created successfully")
    except Exception as e:
        print(f"❌ Error creating valid ParsedResult: {e}")
        return False
    
    # Test with None values
    try:
        result = ParsedResult(
            BaseProductName="Test Product",
            Option1Name=None,
            Option1Value=None
        )
        print("✅ ParsedResult with None values handled correctly")
    except Exception as e:
        print(f"❌ Error handling None values: {e}")
        return False
    
    # Test with empty strings
    try:
        result = ParsedResult(
            BaseProductName="",
            Option1Name="",
            Option1Value=""
        )
        # Empty strings should be converted to None for options
        assert result.Option1Name is None
        assert result.Option1Value is None
        print("✅ Empty strings converted to None correctly")
    except Exception as e:
        print(f"❌ Error handling empty strings: {e}")
        return False
    
    return True


def test_extraction_result_document():
    """Test ExtractionResultDocument validation."""
    print("\n🔍 Testing ExtractionResultDocument validation...")
    
    # Test valid document
    try:
        parsed = ParsedResult(BaseProductName="Test Product")
        doc = ExtractionResultDocument(
            extraction_id="test-123",
            row_index=0,
            original="Original Product Name",
            parsed=parsed,
            errors=[]
        )
        print("✅ Valid ExtractionResultDocument created successfully")
    except Exception as e:
        print(f"❌ Error creating valid ExtractionResultDocument: {e}")
        return False
    
    # Test with errors
    try:
        parsed = ParsedResult(BaseProductName="Test Product")
        doc = ExtractionResultDocument(
            extraction_id="test-123",
            row_index=0,
            original="Original Product Name",
            parsed=parsed,
            errors=["Test error", None, 123]  # Mixed types
        )
        # Should convert all to strings
        assert all(isinstance(error, str) for error in doc.errors)
        print("✅ Error list validation working correctly")
    except Exception as e:
        print(f"❌ Error handling error list: {e}")
        return False
    
    return True


def test_extractor_response_parsing():
    """Test extractor response parsing with mock data."""
    print("\n🔍 Testing extractor response parsing...")
    
    try:
        # Create a mock extractor (without API key for testing)
        extractor = ProductExtractor(
            api_key="test-key",
            prompt_description="Test prompt"
        )
        
        # Test empty result handling
        empty_results = extractor._create_empty_result()
        assert isinstance(empty_results, dict)
        assert "BaseProductName" in empty_results
        print("✅ Empty result creation working")
        
        # Test single extraction parsing with mock data
        class MockExtraction:
            def __init__(self, attributes):
                self.attributes = attributes
        
        # Test valid extraction
        mock_ext = MockExtraction({
            "base_product_name": "Test Product",
            "option1_name": "Size",
            "option1_value": "Large"
        })
        
        result = extractor._parse_single_extraction(mock_ext, 0)
        assert result["BaseProductName"] == "Test Product"
        assert result["Option1Name"] == "Size"
        assert result["Option1Value"] == "Large"
        print("✅ Single extraction parsing working")
        
        # Test extraction with invalid data
        mock_ext_invalid = MockExtraction({
            "base_product_name": None,
            "option1_name": 123,
            "option1_value": []
        })
        
        result = extractor._parse_single_extraction(mock_ext_invalid, 0)
        assert isinstance(result["BaseProductName"], str)
        print("✅ Invalid data handling working")
        
    except Exception as e:
        print(f"❌ Error testing extractor: {e}")
        return False
    
    return True


def test_data_transformation():
    """Test data transformation from extractor to MongoDB format."""
    print("\n🔍 Testing data transformation...")
    
    try:
        # Mock parsed results from extractor
        parsed_results = [
            {
                "BaseProductName": "Test Product 1",
                "Option1Name": "Size",
                "Option1Value": "Large",
                "Option2Name": "Color",
                "Option2Value": "Blue"
            },
            {
                "BaseProductName": "Test Product 2",
                "Option1Name": "Weight",
                "Option1Value": "500g"
            },
            {
                "BaseProductName": "Test Product 3"
                # No options
            }
        ]
        
        # Test transformation (simulate worker logic)
        from worker.cli import ExtractionWorker
        worker = ExtractionWorker.__new__(ExtractionWorker)  # Create without __init__
        
        for i, parsed in enumerate(parsed_results):
            try:
                transformed = worker._transform_parsed_result(parsed)
                assert isinstance(transformed, ParsedResult)
                assert transformed.BaseProductName == parsed["BaseProductName"]
                print(f"✅ Transformation {i+1} successful")
            except Exception as e:
                print(f"❌ Transformation {i+1} failed: {e}")
                return False
        
    except Exception as e:
        print(f"❌ Error testing data transformation: {e}")
        return False
    
    return True


def test_mongodb_document_structure():
    """Test MongoDB document structure compliance."""
    print("\n🔍 Testing MongoDB document structure...")
    
    try:
        # Create test documents
        parsed = ParsedResult(
            BaseProductName="Test Product",
            Option1Name="Size",
            Option1Value="Large"
        )
        
        doc = ExtractionResultDocument(
            extraction_id="test-123",
            row_index=0,
            original="Original Product Name",
            parsed=parsed
        )
        
        # Convert to dict (MongoDB format)
        doc_dict = doc.model_dump()
        
        # Validate structure
        required_fields = ['extraction_id', 'row_index', 'original', 'parsed', 'errors']
        for field in required_fields:
            if field not in doc_dict:
                print(f"❌ Missing required field: {field}")
                return False
        
        # Validate parsed structure
        parsed_dict = doc_dict['parsed']
        if not isinstance(parsed_dict, dict):
            print("❌ Parsed field is not a dictionary")
            return False
        
        if 'BaseProductName' not in parsed_dict:
            print("❌ Missing BaseProductName in parsed field")
            return False
        
        print("✅ MongoDB document structure is valid")
        
    except Exception as e:
        print(f"❌ Error testing MongoDB structure: {e}")
        return False
    
    return True


def main():
    """Main test function."""
    print("🚀 Data Pipeline Validation Test")
    print("=" * 50)
    
    tests = [
        test_parsed_result_validation,
        test_extraction_result_document,
        test_extractor_response_parsing,
        test_data_transformation,
        test_mongodb_document_structure
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All data pipeline tests passed!")
        print("\nThe data processing pipeline is robust and ready for production.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
