#!/usr/bin/env python3
"""
Script to validate the setup and basic functionality.
"""

import json
import sys
from pathlib import Path


def validate_prompts_file():
    """Validate that prompts file exists and has correct structure."""
    print("🔍 Validating prompts file...")
    
    prompts_file = Path("prompts/categories.json")
    if not prompts_file.exists():
        print("❌ prompts/categories.json not found")
        return False
    
    try:
        with open(prompts_file, 'r') as f:
            data = json.load(f)
        
        if "templates" not in data:
            print("❌ Missing 'templates' key in prompts file")
            return False
        
        templates = data["templates"]
        if len(templates) == 0:
            print("❌ No templates found in prompts file")
            return False
        
        print(f"✅ Found {len(templates)} prompt templates")
        
        # Validate each template
        required_fields = ["category", "label", "prompt_description", "schema_hint", "is_builtin"]
        for i, template in enumerate(templates):
            for field in required_fields:
                if field not in template:
                    print(f"❌ Template {i} missing required field: {field}")
                    return False
        
        print("✅ All templates have required fields")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in prompts file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating prompts file: {e}")
        return False


def validate_project_structure():
    """Validate that all required directories and files exist."""
    print("🔍 Validating project structure...")
    
    required_paths = [
        "langextract_core/__init__.py",
        "langextract_core/models.py",
        "langextract_core/extractor.py",
        "langextract_core/batching.py",
        "langextract_core/storage.py",
        "langextract_core/prompts_registry.py",
        "api/__init__.py",
        "api/main.py",
        "api/routers/prompts.py",
        "api/routers/uploads.py",
        "api/routers/extractions.py",
        "worker/__init__.py",
        "worker/cli.py",
        "docker-compose.yml",
        "infra/Dockerfile.api",
        "infra/Dockerfile.worker",
        ".env.example",
        "scripts/seed_prompts.py",
        "scripts/parse_prompts_simple.py",
        "scripts/test_aws_config.py",
        "scripts/test_langextract_parsing.py",
        "scripts/validate_data_pipeline.py",
        "requirements.txt"
    ]
    
    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)
    
    if missing_paths:
        print("❌ Missing required files:")
        for path in missing_paths:
            print(f"   - {path}")
        return False
    
    print("✅ All required files present")
    return True


def validate_requirements():
    """Validate requirements.txt has necessary dependencies."""
    print("🔍 Validating requirements.txt...")
    
    try:
        with open("requirements.txt", 'r') as f:
            content = f.read()
        
        required_packages = [
            "fastapi",
            "uvicorn",
            "boto3",
            "pymongo",
            "pydantic",
            "langextract",
            "python-dotenv"
        ]
        
        missing_packages = []
        for package in required_packages:
            if package not in content:
                missing_packages.append(package)
        
        if missing_packages:
            print("❌ Missing required packages:")
            for package in missing_packages:
                print(f"   - {package}")
            return False
        
        print("✅ All required packages present in requirements.txt")
        return True
        
    except Exception as e:
        print(f"❌ Error validating requirements.txt: {e}")
        return False


def main():
    """Main validation function."""
    print("🚀 LangExtract Platform Setup Validation")
    print("=" * 50)
    
    all_valid = True
    
    # Run validations
    validations = [
        validate_project_structure,
        validate_requirements,
        validate_prompts_file
    ]
    
    for validation in validations:
        if not validation():
            all_valid = False
        print()
    
    # Summary
    if all_valid:
        print("🎉 All validations passed! The platform setup looks good.")
        print("\nNext steps:")
        print("1. Install and start MongoDB locally (or use managed service)")
        print("2. Configure .env with your API keys and AWS settings")
        print("3. Test AWS configuration: python3 scripts/test_aws_config.py")
        print("4. Test LangExtract parsing: python3 scripts/test_langextract_parsing.py")
        print("5. Validate data pipeline: python3 scripts/validate_data_pipeline.py")
        print("6. Run: docker-compose up --build")
        print("7. Access API docs at: http://localhost:8080/docs")
        return 0
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
