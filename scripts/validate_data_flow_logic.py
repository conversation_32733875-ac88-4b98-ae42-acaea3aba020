#!/usr/bin/env python3
"""
Validate data flow logic improvements without requiring external dependencies.
"""

import ast
import re
from pathlib import Path


def validate_extractor_improvements():
    """Validate extractor exception handling improvements."""
    print("🔍 Validating Extractor Exception Handling...")
    
    extractor_file = Path("langextract_core/extractor.py")
    if not extractor_file.exists():
        print("❌ extractor.py not found")
        return False
    
    content = extractor_file.read_text()
    
    # Check for improved logging
    if "✅ LangExtract API call successful" not in content:
        print("❌ Missing success logging for LangExtract API")
        return False
    
    if "📊 Parsing summary:" not in content:
        print("❌ Missing parsing summary logging")
        return False
    
    # Check for better error handling
    if "💥 Unexpected error in batch processing" not in content:
        print("❌ Missing improved error classification")
        return False
    
    # Check that we return empty results instead of raising
    if "return [self._create_empty_result() for _ in rows]" not in content:
        print("❌ Missing fallback to empty results")
        return False
    
    print("✅ Extractor improvements validation passed")
    return True


def validate_worker_improvements():
    """Validate worker data flow logging improvements."""
    print("\n🔍 Validating Worker Data Flow Logging...")
    
    worker_file = Path("worker/cli.py")
    if not worker_file.exists():
        print("❌ worker/cli.py not found")
        return False
    
    content = worker_file.read_text()
    
    # Check for batch processing logging
    if "🔄 Processing batch" not in content:
        print("❌ Missing batch processing start logging")
        return False
    
    if "✅ Extractor returned" not in content:
        print("❌ Missing extractor success logging")
        return False
    
    # Check for transformation logging
    if "📊 Transformation summary:" not in content:
        print("❌ Missing transformation summary logging")
        return False
    
    # Check for MongoDB insertion logging
    if "💾 Inserting" not in content:
        print("❌ Missing MongoDB insertion logging")
        return False
    
    if "✅ Successfully inserted all" not in content:
        print("❌ Missing MongoDB success logging")
        return False
    
    # Check for improved error classification
    if "🌐 LangExtract API error" not in content:
        print("❌ Missing LangExtract error classification")
        return False
    
    if "💾 Database error" not in content:
        print("❌ Missing database error classification")
        return False
    
    if "📋 Data validation error" not in content:
        print("❌ Missing validation error classification")
        return False
    
    print("✅ Worker improvements validation passed")
    return True


def validate_storage_improvements():
    """Validate storage layer logging improvements."""
    print("\n🔍 Validating Storage Layer Improvements...")
    
    storage_file = Path("langextract_core/storage.py")
    if not storage_file.exists():
        print("❌ storage.py not found")
        return False
    
    content = storage_file.read_text()
    
    # Check for document validation logging
    if "🔍 Validating" not in content:
        print("❌ Missing document validation start logging")
        return False
    
    if "📊 Document validation:" not in content:
        print("❌ Missing validation summary logging")
        return False
    
    # Check for insertion logging
    if "💾 Attempting to insert" not in content:
        print("❌ Missing insertion attempt logging")
        return False
    
    if "✅ Successfully inserted all" not in content:
        print("❌ Missing insertion success logging")
        return False
    
    # Check for detailed validation error logging
    if "❌ Result" not in content:
        print("❌ Missing detailed validation error logging")
        return False
    
    print("✅ Storage improvements validation passed")
    return True


def validate_error_handling_flow():
    """Validate that error handling doesn't catch successful responses."""
    print("\n🔍 Validating Error Handling Flow...")
    
    extractor_file = Path("langextract_core/extractor.py")
    content = extractor_file.read_text()
    
    # Check that the main exception handler is at the right level
    # Look for the pattern where we log success before potential errors
    success_pattern = r'logger\.info\(f"✅ LangExtract API call successful'
    error_pattern = r'except Exception as e:.*💥 Unexpected error'
    
    if not re.search(success_pattern, content):
        print("❌ Missing success logging before error handling")
        return False
    
    if not re.search(error_pattern, content, re.DOTALL):
        print("❌ Missing proper error classification")
        return False
    
    # Check that we don't re-raise exceptions unnecessarily
    if "raise" in content.split("except Exception as e:")[-1].split("def")[0]:
        print("❌ Found unnecessary exception re-raising")
        return False
    
    print("✅ Error handling flow validation passed")
    return True


def validate_logging_levels():
    """Validate that appropriate logging levels are used."""
    print("\n🔍 Validating Logging Levels...")
    
    files_to_check = [
        "langextract_core/extractor.py",
        "worker/cli.py", 
        "langextract_core/storage.py"
    ]
    
    for file_path in files_to_check:
        file_obj = Path(file_path)
        if not file_obj.exists():
            print(f"❌ {file_path} not found")
            return False
        
        content = file_obj.read_text()
        
        # Check for appropriate use of different log levels
        has_info = "logger.info" in content
        has_debug = "logger.debug" in content
        has_warning = "logger.warning" in content
        has_error = "logger.error" in content
        
        if not (has_info and has_debug and has_warning and has_error):
            print(f"❌ {file_path} missing appropriate logging levels")
            print(f"   Info: {has_info}, Debug: {has_debug}, Warning: {has_warning}, Error: {has_error}")
            return False
    
    print("✅ Logging levels validation passed")
    return True


def validate_data_flow_tracking():
    """Validate that data flow is properly tracked through the pipeline."""
    print("\n🔍 Validating Data Flow Tracking...")
    
    # Check that each stage logs its input and output
    worker_file = Path("worker/cli.py")
    content = worker_file.read_text()
    
    # Should log batch size at start
    if "with {len(batch_rows)} rows" not in content:
        print("❌ Missing batch size logging")
        return False
    
    # Should log extractor results
    if "Extractor returned {len(parsed_results)} results" not in content:
        print("❌ Missing extractor results count logging")
        return False
    
    # Should log transformation results
    if "transformation_successes" not in content:
        print("❌ Missing transformation success tracking")
        return False
    
    # Should log MongoDB insertion results
    if "inserted_count" not in content:
        print("❌ Missing MongoDB insertion count tracking")
        return False
    
    print("✅ Data flow tracking validation passed")
    return True


def main():
    """Main validation function."""
    print("🚀 Data Flow Logic Validation")
    print("=" * 50)
    
    validations = [
        validate_extractor_improvements,
        validate_worker_improvements,
        validate_storage_improvements,
        validate_error_handling_flow,
        validate_logging_levels,
        validate_data_flow_tracking
    ]
    
    passed = 0
    failed = 0
    
    for validation in validations:
        try:
            if validation():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Validation {validation.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Validation Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All data flow logic validations passed!")
        print("\nKey improvements implemented:")
        print("• ✅ Enhanced exception handling to not catch successful API responses")
        print("• ✅ Comprehensive logging throughout the data pipeline")
        print("• ✅ Error classification for better debugging")
        print("• ✅ Data flow tracking from LangExtract to MongoDB")
        print("• ✅ Validation and insertion success/failure logging")
        print("• ✅ Graceful error recovery without stopping the pipeline")
        return 0
    else:
        print("❌ Some validations failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
