#!/usr/bin/env python3
"""
Simple script to parse prompt-examples.txt without dependencies.
"""

import json
import re
from pathlib import Path


def parse_prompt_examples_to_json(input_file="prompt-examples.txt", output_file="prompts/categories.json"):
    """Parse prompt-examples.txt file and convert to JSON format."""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the content to extract prompt categories
        templates = []
        
        # Pattern to match numbered sections
        section_pattern = r'(\d+)\)\s+([^—]+)—([^\n]+)'
        prompt_pattern = r'prompt_description = textwrap\.dedent\("""\\\s*(.*?)\s*"""\)'

        sections = re.split(r'\n(?=\d+\))', content)

        for section in sections:
            if not section.strip():
                continue

            # Extract section header
            header_match = re.search(section_pattern, section)
            if not header_match:
                continue

            section_num = header_match.group(1)
            category_name = header_match.group(2).strip()
            constraints = header_match.group(3).strip()

            # Clean up constraints - remove any trailing import statements
            constraints = re.sub(r'\s*import\s+.*$', '', constraints).strip()

            # Extract prompt description
            prompt_match = re.search(prompt_pattern, section, re.DOTALL)
            if not prompt_match:
                continue

            prompt_description = prompt_match.group(1).strip()
            
            # Create category identifier
            category_id = category_name.lower().replace(' ', '_').replace('&', '').replace(',', '').strip()
            category_id = re.sub(r'[^\w]', '_', category_id)
            category_id = re.sub(r'_+', '_', category_id).strip('_')
            
            # Determine max options based on content
            max_options = 2  # Default
            if 'option3' in prompt_description.lower():
                max_options = 3
            elif 'option2' in prompt_description.lower():
                max_options = 2
            else:
                max_options = 1
            
            template = {
                "category": category_id,
                "label": f"{category_name} — {constraints}",
                "prompt_description": prompt_description,
                "schema_hint": {"max_options": max_options},
                "is_builtin": True
            }
            
            templates.append(template)
        
        # Create output structure
        output_data = {
            "version": "1.0",
            "generated_from": input_file,
            "templates": templates
        }
        
        # Ensure output directory exists
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"Parsed {len(templates)} prompt templates to {output_file}")
        
        # Print categories for verification
        print("Categories found:")
        for template in templates:
            print(f"  - {template['category']}: {template['label']}")
        
        return output_data
        
    except Exception as e:
        print(f"Error parsing prompt examples: {e}")
        raise


if __name__ == "__main__":
    parse_prompt_examples_to_json()
