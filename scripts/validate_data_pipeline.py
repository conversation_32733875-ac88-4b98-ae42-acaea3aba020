#!/usr/bin/env python3
"""
Validate data pipeline structure and logic without requiring dependencies.
"""

import ast
import re
from pathlib import Path


def validate_s3_key_generation():
    """Validate S3 key generation logic."""
    print("🔍 Validating S3 key generation...")
    
    uploads_file = Path("api/routers/uploads.py")
    if not uploads_file.exists():
        print("❌ uploads.py not found")
        return False
    
    content = uploads_file.read_text()
    
    # Check for sanitize_filename function
    if "def sanitize_filename" not in content:
        print("❌ sanitize_filename function not found")
        return False
    
    # Check for proper S3 key generation
    if "sanitize_filename(request.file_name)" not in content:
        print("❌ S3 key generation doesn't use sanitize_filename")
        return False
    
    # Check for regex import
    if "import re" not in content:
        print("❌ Missing regex import for filename sanitization")
        return False
    
    print("✅ S3 key generation validation passed")
    return True


def validate_extractor_improvements():
    """Validate extractor response parsing improvements."""
    print("\n🔍 Validating extractor improvements...")
    
    extractor_file = Path("langextract_core/extractor.py")
    if not extractor_file.exists():
        print("❌ extractor.py not found")
        return False
    
    content = extractor_file.read_text()
    
    # Check for improved error handling
    if "_parse_single_extraction" not in content:
        print("❌ _parse_single_extraction method not found")
        return False
    
    if "_create_empty_result" not in content:
        print("❌ _create_empty_result method not found")
        return False
    
    # Check for validation of LangExtract response
    if "hasattr(result, 'extractions')" not in content:
        print("❌ Missing LangExtract response validation")
        return False
    
    # Check for handling mismatched extraction counts
    if "extractions_count != len(rows)" not in content:
        print("❌ Missing extraction count validation")
        return False
    
    print("✅ Extractor improvements validation passed")
    return True


def validate_worker_transformation():
    """Validate worker data transformation logic."""
    print("\n🔍 Validating worker transformation...")
    
    worker_file = Path("worker/cli.py")
    if not worker_file.exists():
        print("❌ worker/cli.py not found")
        return False
    
    content = worker_file.read_text()
    
    # Check for transformation method
    if "_transform_parsed_result" not in content:
        print("❌ _transform_parsed_result method not found")
        return False
    
    # Check for error handling in result creation
    if "try:" not in content or "except Exception as e:" not in content:
        print("❌ Missing error handling in worker")
        return False
    
    # Check for proper ParsedResult creation
    if "ParsedResult(**result_data)" not in content:
        print("❌ Missing proper ParsedResult creation")
        return False
    
    print("✅ Worker transformation validation passed")
    return True


def validate_model_validation():
    """Validate Pydantic model improvements."""
    print("\n🔍 Validating model validation...")
    
    models_file = Path("langextract_core/models.py")
    if not models_file.exists():
        print("❌ models.py not found")
        return False
    
    content = models_file.read_text()
    
    # Check for validator import
    if "from pydantic import BaseModel, Field, validator" not in content:
        print("❌ Missing validator import")
        return False
    
    # Check for ParsedResult validators
    if "@validator('BaseProductName')" not in content:
        print("❌ Missing BaseProductName validator")
        return False
    
    if "@validator('Option1Name', 'Option2Name', 'Option3Name')" not in content:
        print("❌ Missing option name validators")
        return False
    
    # Check for ExtractionResultDocument validators
    if "@validator('extraction_id')" not in content:
        print("❌ Missing extraction_id validator")
        return False
    
    print("✅ Model validation improvements passed")
    return True


def validate_storage_improvements():
    """Validate MongoDB storage improvements."""
    print("\n🔍 Validating storage improvements...")
    
    storage_file = Path("langextract_core/storage.py")
    if not storage_file.exists():
        print("❌ storage.py not found")
        return False
    
    content = storage_file.read_text()
    
    # Check for improved error handling in insert_extraction_results
    insert_method = re.search(
        r'def insert_extraction_results.*?(?=def|\Z)', 
        content, 
        re.DOTALL
    )
    
    if not insert_method:
        print("❌ insert_extraction_results method not found")
        return False
    
    method_content = insert_method.group(0)
    
    # Check for validation logic
    if "isinstance(result, ExtractionResultDocument)" not in method_content:
        print("❌ Missing document type validation")
        return False
    
    if "required_fields" not in method_content:
        print("❌ Missing required fields validation")
        return False
    
    # Check for partial insertion handling
    if "writeErrors" not in method_content:
        print("❌ Missing partial insertion error handling")
        return False
    
    print("✅ Storage improvements validation passed")
    return True


def validate_upload_schema():
    """Validate upload schema improvements."""
    print("\n🔍 Validating upload schema...")
    
    schema_file = Path("api/schemas/uploads.py")
    if not schema_file.exists():
        print("❌ uploads schema not found")
        return False
    
    content = schema_file.read_text()
    
    # Check for validator import
    if "from pydantic import BaseModel, Field, validator" not in content:
        print("❌ Missing validator import in uploads schema")
        return False
    
    # Check for file_name validation
    if "@validator('file_name')" not in content:
        print("❌ Missing file_name validator")
        return False
    
    # Check for content_type validation
    if "@validator('content_type')" not in content:
        print("❌ Missing content_type validator")
        return False
    
    print("✅ Upload schema validation passed")
    return True


def main():
    """Main validation function."""
    print("🚀 Data Pipeline Structure Validation")
    print("=" * 50)
    
    validations = [
        validate_s3_key_generation,
        validate_extractor_improvements,
        validate_worker_transformation,
        validate_model_validation,
        validate_storage_improvements,
        validate_upload_schema
    ]
    
    passed = 0
    failed = 0
    
    for validation in validations:
        try:
            if validation():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Validation {validation.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Validation Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All data pipeline validations passed!")
        print("\nKey improvements implemented:")
        print("• ✅ S3 key sanitization for safe file uploads")
        print("• ✅ Robust LangExtract response parsing")
        print("• ✅ Data transformation with error handling")
        print("• ✅ Pydantic model validation")
        print("• ✅ MongoDB insertion with partial failure handling")
        print("• ✅ Input validation for API requests")
        return 0
    else:
        print("❌ Some validations failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
