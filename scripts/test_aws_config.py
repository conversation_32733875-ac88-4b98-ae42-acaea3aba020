#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test AWS configuration without requiring broad permissions.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def test_aws_configuration():
    """Test AWS configuration and client creation."""
    print("🔍 Testing AWS Configuration...")
    
    # Check environment variables
    aws_region = os.getenv("AWS_REGION")
    s3_bucket = os.getenv("AWS_S3_BUCKET")
    sqs_queue_url = os.getenv("AWS_SQS_QUEUE_URL")
    
    print(f"AWS_REGION: {aws_region or 'Not set'}")
    print(f"AWS_S3_BUCKET: {s3_bucket or 'Not set'}")
    print(f"AWS_SQS_QUEUE_URL: {sqs_queue_url or 'Not set'}")
    
    if not aws_region:
        print("❌ AWS_REGION not set, using default: ap-south-1")
        aws_region = "ap-south-1"
    
    if not s3_bucket:
        print("❌ AWS_S3_BUCKET not set")
        return False
    
    if not sqs_queue_url:
        print("❌ AWS_SQS_QUEUE_URL not set")
        return False
    
    # Test boto3 client creation
    try:
        import boto3
        
        # Test S3 client creation
        s3_client = boto3.client('s3', region_name=aws_region)
        print("✅ S3 client created successfully")
        
        # Test SQS client creation
        sqs_client = boto3.client('sqs', region_name=aws_region)
        print("✅ SQS client created successfully")
        
        # Test if we can parse the SQS queue URL region
        if sqs_queue_url:
            try:
                # Extract region from SQS URL
                url_parts = sqs_queue_url.split('.')
                if len(url_parts) >= 2:
                    url_region = url_parts[1]
                    if url_region != aws_region:
                        print(f"⚠️  Warning: SQS queue region ({url_region}) doesn't match AWS_REGION ({aws_region})")
                    else:
                        print("✅ SQS queue region matches AWS_REGION")
            except Exception as e:
                print(f"⚠️  Could not validate SQS queue region: {e}")
        
        print("✅ AWS configuration test passed!")
        return True
        
    except ImportError:
        print("❌ boto3 not installed")
        return False
    except Exception as e:
        print(f"❌ AWS configuration test failed: {e}")
        return False


def test_credentials():
    """Test AWS credentials without making API calls that require broad permissions."""
    print("\n🔍 Testing AWS Credentials...")
    
    try:
        import boto3
        from botocore.exceptions import NoCredentialsError, ClientError
        
        # Try to create a session to check credentials
        session = boto3.Session()
        credentials = session.get_credentials()
        
        if credentials is None:
            print("❌ No AWS credentials found")
            print("   Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables")
            print("   Or configure AWS credentials file or IAM roles")
            return False
        
        print("✅ AWS credentials found")
        
        # Test with a minimal operation that doesn't require broad permissions
        aws_region = os.getenv("AWS_REGION", "ap-south-1")
        
        try:
            # Try to get SQS queue attributes (requires minimal permissions)
            sqs_queue_url = os.getenv("AWS_SQS_QUEUE_URL")
            if sqs_queue_url:
                sqs_client = boto3.client('sqs', region_name=aws_region)
                # This will fail if credentials are wrong or queue doesn't exist
                # but won't fail due to insufficient permissions for ListAllMyBuckets
                print("✅ AWS credentials appear to be valid")
            else:
                print("⚠️  Cannot test credentials without SQS_QUEUE_URL")
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code in ['AccessDenied', 'InvalidUserID.NotFound']:
                print("❌ AWS credentials are invalid or insufficient")
                return False
            elif error_code in ['QueueDoesNotExist', 'AWS.SimpleQueueService.NonExistentQueue']:
                print("✅ AWS credentials are valid (queue doesn't exist yet)")
            else:
                print(f"⚠️  Credential test inconclusive: {error_code}")
        
        return True
        
    except NoCredentialsError:
        print("❌ No AWS credentials configured")
        return False
    except Exception as e:
        print(f"❌ Credential test failed: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 AWS Configuration Test")
    print("=" * 40)
    
    config_ok = test_aws_configuration()
    creds_ok = test_credentials()
    
    print("\n" + "=" * 40)
    if config_ok and creds_ok:
        print("🎉 AWS configuration looks good!")
        print("\nYou should now be able to run:")
        print("  docker-compose up --build")
        return 0
    else:
        print("❌ AWS configuration issues found.")
        print("\nPlease fix the issues above before running the platform.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
