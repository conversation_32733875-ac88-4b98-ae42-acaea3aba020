#!/usr/bin/env python3
"""
Debug script to trace data transformation with the exact LangExtract response format.
"""

import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_mock_langextract_extraction():
    """Create a mock LangExtract extraction object with the exact format from your logs."""
    
    class MockExtraction:
        def __init__(self, data):
            # This mimics the actual LangExtract response structure
            self.product = data.get("product", "")
            self.product_attributes = data.get("product_attributes", {})
            
            # Store raw data for debugging
            self._raw_data = data
    
    # This is the exact data from your debug logs
    extraction_data = {
        "product": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
        "product_attributes": {
            "base_product_name": "502 FS CBD Oil",
            "option1_name": "Size",
            "option1_value": "1oz",
            "option2_name": "Strength",
            "option2_value": "250mg",
            "option3_name": "Flavor",
            "option3_value": "Pink Lemonade"
        }
    }
    
    return MockExtraction(extraction_data)


def test_extractor_parsing():
    """Test the extractor parsing with the exact data format."""
    print("🔍 Testing Extractor Parsing with Debug Data...")
    
    try:
        from langextract_core.extractor import ProductExtractor
        
        # Create extractor
        extractor = ProductExtractor(
            api_key="test-key",
            prompt_description="Test prompt"
        )
        
        # Create mock extraction
        mock_extraction = create_mock_langextract_extraction()
        
        print(f"📝 Mock extraction type: {type(mock_extraction)}")
        print(f"📝 Mock extraction has product_attributes: {hasattr(mock_extraction, 'product_attributes')}")
        print(f"📝 Mock extraction product_attributes: {mock_extraction.product_attributes}")
        
        # Test the _extract_attributes method
        print("\n🔍 Testing _extract_attributes method...")
        attrs = extractor._extract_attributes(mock_extraction, 0)
        print(f"📝 Extracted attributes: {attrs}")
        
        if not attrs:
            print("❌ No attributes extracted!")
            return None
        
        # Test the _parse_single_extraction method
        print("\n🔍 Testing _parse_single_extraction method...")
        parsed_result = extractor._parse_single_extraction(mock_extraction, 0)
        print(f"📝 Parsed result: {parsed_result}")
        
        return parsed_result
        
    except ImportError as e:
        print(f"⚠️  Cannot import extractor (missing dependencies): {e}")
        return test_parsing_logic_manually()
    except Exception as e:
        print(f"❌ Error testing extractor: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_parsing_logic_manually():
    """Test the parsing logic manually without dependencies."""
    print("\n🔍 Testing parsing logic manually...")
    
    mock_extraction = create_mock_langextract_extraction()
    
    # Simulate _extract_attributes logic
    attrs = None
    
    # Check for product_attributes (this should match)
    if hasattr(mock_extraction, 'product_attributes'):
        attrs = mock_extraction.product_attributes
        if isinstance(attrs, dict) and attrs:
            print(f"✅ Found product_attributes: {attrs}")
        else:
            print(f"❌ product_attributes is empty or not dict: {attrs}")
    else:
        print("❌ No product_attributes found")
    
    if not attrs:
        print("❌ No attributes extracted")
        return None
    
    # Simulate _parse_single_extraction logic
    base_name = attrs.get("base_product_name", "")
    print(f"📝 Base product name: '{base_name}'")
    
    parsed_result = {
        "BaseProductName": base_name.strip()
    }
    
    # Parse options
    for i in range(1, 4):
        name_key = f"option{i}_name"
        value_key = f"option{i}_value"
        
        print(f"📝 Checking for {name_key} and {value_key}")
        
        if name_key in attrs and value_key in attrs:
            option_name = str(attrs[name_key]).strip()
            option_value = str(attrs[value_key]).strip()
            
            if option_name and option_value:
                parsed_result[f"Option{i}Name"] = option_name
                parsed_result[f"Option{i}Value"] = option_value
                print(f"✅ Added Option{i}: {option_name} = {option_value}")
            else:
                print(f"⚠️  Skipping empty Option{i}")
        else:
            print(f"📝 No Option{i} found")
    
    print(f"📝 Final parsed result: {parsed_result}")
    return parsed_result


def test_worker_transformation(parsed_result):
    """Test the worker transformation logic."""
    print("\n🔍 Testing Worker Transformation...")
    
    if not parsed_result:
        print("❌ No parsed result to transform")
        return None
    
    try:
        from worker.cli import ExtractionWorker
        from langextract_core.models import ParsedResult, ExtractionResultDocument
        
        # Create worker instance
        worker = ExtractionWorker.__new__(ExtractionWorker)
        
        print(f"📝 Input to transformation: {parsed_result}")
        
        # Test transformation
        transformed = worker._transform_parsed_result(parsed_result)
        print(f"📝 Transformed result: {transformed}")
        print(f"📝 Transformed type: {type(transformed)}")
        
        # Test document creation
        doc = ExtractionResultDocument(
            extraction_id="test-123",
            row_index=0,
            original="502 FS CBD Oil Pink Lemonade 250mg 1oz",
            parsed=transformed
        )
        
        print(f"📝 Created document: {doc}")
        
        # Convert to dict (MongoDB format)
        doc_dict = doc.model_dump()
        print(f"📝 Document dict: {doc_dict}")
        print(f"📝 Document parsed field: {doc_dict.get('parsed', 'MISSING')}")
        
        return doc
        
    except ImportError as e:
        print(f"⚠️  Cannot import worker (missing dependencies): {e}")
        return test_transformation_manually(parsed_result)
    except Exception as e:
        print(f"❌ Error testing worker transformation: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_transformation_manually(parsed_result):
    """Test transformation logic manually."""
    print("\n🔍 Testing transformation manually...")
    
    # Simulate worker transformation
    result_data = {
        "BaseProductName": parsed_result.get("BaseProductName", "")
    }
    
    option_mapping = {
        1: ("Option1Name", "Option1Value"),
        2: ("Option2Name", "Option2Value"),
        3: ("Option3Name", "Option3Value")
    }
    
    for i, (name_field, value_field) in option_mapping.items():
        option_name_key = f"Option{i}Name"
        option_value_key = f"Option{i}Value"
        
        if option_name_key in parsed_result and option_value_key in parsed_result:
            result_data[name_field] = parsed_result[option_name_key]
            result_data[value_field] = parsed_result[option_value_key]
            print(f"✅ Mapped Option{i}: {result_data[name_field]} = {result_data[value_field]}")
        else:
            result_data[name_field] = None
            result_data[value_field] = None
            print(f"⚠️  Option{i} not found, set to None")
    
    print(f"📝 Final transformation result: {result_data}")
    return result_data


def main():
    """Main debug function."""
    print("🚀 Data Transformation Debug Test")
    print("=" * 60)
    print("Using exact data from your debug logs:")
    print(json.dumps({
        "product": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
        "product_attributes": {
            "base_product_name": "502 FS CBD Oil",
            "option1_name": "Size",
            "option1_value": "1oz",
            "option2_name": "Strength",
            "option2_value": "250mg",
            "option3_name": "Flavor",
            "option3_value": "Pink Lemonade"
        }
    }, indent=2))
    print("=" * 60)
    
    # Test extractor parsing
    parsed_result = test_extractor_parsing()
    if not parsed_result:
        print("❌ Extractor parsing failed")
        return 1
    
    # Test worker transformation
    transformed_result = test_worker_transformation(parsed_result)
    if not transformed_result:
        print("❌ Worker transformation failed")
        return 1
    
    print("\n" + "=" * 60)
    print("🎉 Data transformation test completed!")
    print("\nExpected MongoDB document structure:")
    print(json.dumps({
        "extraction_id": "test-123",
        "row_index": 0,
        "original": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
        "parsed": {
            "BaseProductName": "502 FS CBD Oil",
            "Option1Name": "Size",
            "Option1Value": "1oz",
            "Option2Name": "Strength",
            "Option2Value": "250mg",
            "Option3Name": "Flavor",
            "Option3Value": "Pink Lemonade"
        },
        "errors": []
    }, indent=2))
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
