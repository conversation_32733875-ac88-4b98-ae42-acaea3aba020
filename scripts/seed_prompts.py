#!/usr/bin/env python3
"""
Script to parse prompt-examples.txt and seed MongoDB with prompt templates.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path to import langextract_core
sys.path.insert(0, str(Path(__file__).parent.parent))

from langextract_core.prompts_registry import parse_prompt_examples_to_json
from langextract_core.storage import MongoStorage
from langextract_core.models import PromptTemplate
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function to parse and seed prompt templates."""
    try:
        # Parse prompt examples to JSON
        logger.info("Parsing prompt-examples.txt...")
        data = parse_prompt_examples_to_json(
            input_file="prompt-examples.txt",
            output_file="prompts/categories.json"
        )
        
        # Convert to PromptTemplate objects
        templates = []
        for template_data in data['templates']:
            template = PromptTemplate(**template_data)
            templates.append(template)
        
        logger.info(f"Parsed {len(templates)} templates")
        
        # Seed MongoDB
        logger.info("Connecting to MongoDB...")
        storage = MongoStorage()
        
        logger.info("Seeding prompt templates...")
        inserted_count = storage.seed_prompt_templates(templates)
        
        logger.info(f"Successfully seeded {inserted_count} prompt templates")
        
        # Verify by listing categories
        categories = storage.get_prompt_categories()
        logger.info(f"Available categories: {[cat['category'] for cat in categories]}")
        
    except Exception as e:
        logger.error(f"Error seeding prompts: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
