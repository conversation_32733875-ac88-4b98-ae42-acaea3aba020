#!/usr/bin/env python3
"""
Test script to validate the complete data flow from LangExtract to MongoDB.
"""

import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging to see all debug messages
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_mock_langextract_response():
    """Create a mock LangExtract response in the actual format."""
    class MockResult:
        def __init__(self):
            self.extractions = [
                MockExtraction({
                    "product": "502 FS CBD Oil 1500mg 1oz Spearmint",
                    "product_attributes": {
                        "base_product_name": "502 FS CBD Oil",
                        "option1_name": "Size",
                        "option1_value": "1oz",
                        "option2_name": "Strength", 
                        "option2_value": "1500mg",
                        "option3_name": "Flavor",
                        "option3_value": "Spearmint"
                    }
                }),
                MockExtraction({
                    "product": "Hemp Gummies 25mg Cherry",
                    "product_attributes": {
                        "base_product_name": "Hemp Gummies",
                        "option1_name": "Strength",
                        "option1_value": "25mg",
                        "option2_name": "Flavor",
                        "option2_value": "Cherry"
                    }
                })
            ]
    
    class MockExtraction:
        def __init__(self, data):
            self.product_attributes = data.get("product_attributes", {})
            self._data = data
    
    return MockResult()


def test_extractor_processing():
    """Test the extractor processing with mock data."""
    print("🔍 Testing Extractor Processing...")
    
    try:
        from langextract_core.extractor import ProductExtractor
        
        # Create extractor
        extractor = ProductExtractor(
            api_key="test-key",
            prompt_description="Test CBD extraction prompt"
        )
        
        # Test input data
        test_rows = [
            "502 FS CBD Oil 1500mg 1oz Spearmint",
            "Hemp Gummies 25mg Cherry"
        ]
        
        # Mock the LangExtract API call
        original_extract = None
        try:
            import langextract as lx
            original_extract = lx.extract
            
            def mock_extract(*args, **kwargs):
                print("🎭 Mock LangExtract API called")
                return create_mock_langextract_response()
            
            lx.extract = mock_extract
            
            # Process the batch
            results = extractor.process_batch(test_rows)
            
            print(f"✅ Extractor returned {len(results)} results")
            for i, result in enumerate(results):
                print(f"  Result {i}: {result}")
            
            return results
            
        except ImportError:
            print("⚠️  LangExtract not available, testing parsing logic directly")
            
            # Test the parsing logic directly
            mock_result = create_mock_langextract_response()
            results = []
            
            for i, ext in enumerate(mock_result.extractions):
                parsed = extractor._parse_single_extraction(ext, i)
                results.append(parsed)
            
            print(f"✅ Direct parsing returned {len(results)} results")
            for i, result in enumerate(results):
                print(f"  Result {i}: {result}")
            
            return results
            
        finally:
            # Restore original function
            if original_extract:
                lx.extract = original_extract
                
    except Exception as e:
        print(f"❌ Error testing extractor: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_worker_transformation():
    """Test the worker transformation logic."""
    print("\n🔍 Testing Worker Transformation...")
    
    try:
        from worker.cli import ExtractionWorker
        from langextract_core.models import ParsedResult, ExtractionResultDocument
        
        # Create worker instance (without full initialization)
        worker = ExtractionWorker.__new__(ExtractionWorker)
        
        # Test data from extractor
        parsed_results = [
            {
                "BaseProductName": "502 FS CBD Oil",
                "Option1Name": "Size",
                "Option1Value": "1oz",
                "Option2Name": "Strength",
                "Option2Value": "1500mg",
                "Option3Name": "Flavor",
                "Option3Value": "Spearmint"
            },
            {
                "BaseProductName": "Hemp Gummies",
                "Option1Name": "Strength",
                "Option1Value": "25mg",
                "Option2Name": "Flavor",
                "Option2Value": "Cherry"
            }
        ]
        
        original_rows = [
            "502 FS CBD Oil 1500mg 1oz Spearmint",
            "Hemp Gummies 25mg Cherry"
        ]
        
        # Test transformation
        result_docs = []
        for i, (original, parsed) in enumerate(zip(original_rows, parsed_results)):
            try:
                # Transform parsed result
                parsed_result = worker._transform_parsed_result(parsed)
                print(f"✅ Transformed result {i}: {parsed_result}")
                
                # Create document
                result_doc = ExtractionResultDocument(
                    extraction_id="test-123",
                    row_index=i,
                    original=original,
                    parsed=parsed_result
                )
                result_docs.append(result_doc)
                print(f"✅ Created document {i}: {result_doc.original}")
                
            except Exception as e:
                print(f"❌ Error transforming result {i}: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"✅ Worker transformation created {len(result_docs)} documents")
        return result_docs
        
    except Exception as e:
        print(f"❌ Error testing worker transformation: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_mongodb_validation():
    """Test MongoDB document validation."""
    print("\n🔍 Testing MongoDB Document Validation...")
    
    try:
        from langextract_core.models import ParsedResult, ExtractionResultDocument
        
        # Test valid document
        parsed = ParsedResult(
            BaseProductName="502 FS CBD Oil",
            Option1Name="Size",
            Option1Value="1oz",
            Option2Name="Strength",
            Option2Value="1500mg",
            Option3Name="Flavor",
            Option3Value="Spearmint"
        )
        
        doc = ExtractionResultDocument(
            extraction_id="test-123",
            row_index=0,
            original="502 FS CBD Oil 1500mg 1oz Spearmint",
            parsed=parsed
        )
        
        # Convert to MongoDB format
        doc_dict = doc.model_dump()
        print(f"✅ Document validation passed")
        print(f"  Document keys: {list(doc_dict.keys())}")
        print(f"  Parsed keys: {list(doc_dict['parsed'].keys())}")
        
        return [doc]
        
    except Exception as e:
        print(f"❌ Error testing MongoDB validation: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main test function."""
    print("🚀 Complete Data Flow Test")
    print("=" * 60)
    
    # Test each stage of the pipeline
    extractor_results = test_extractor_processing()
    if not extractor_results:
        print("❌ Extractor test failed - stopping")
        return 1
    
    worker_results = test_worker_transformation()
    if not worker_results:
        print("❌ Worker transformation test failed - stopping")
        return 1
    
    mongodb_results = test_mongodb_validation()
    if not mongodb_results:
        print("❌ MongoDB validation test failed - stopping")
        return 1
    
    print("\n" + "=" * 60)
    print("🎉 All data flow tests passed!")
    print("\nData flow summary:")
    print(f"• ✅ Extractor: {len(extractor_results)} results processed")
    print(f"• ✅ Worker: {len(worker_results)} documents created")
    print(f"• ✅ MongoDB: {len(mongodb_results)} documents validated")
    print("\nThe complete data pipeline is working correctly!")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
