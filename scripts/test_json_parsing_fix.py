#!/usr/bin/env python3
"""
Test script to validate the JSON parsing fix in the extractor.
"""

import sys
import os
import json
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langextract_core.extractor import LangExtractExtractor

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_json_cleaning():
    """Test the JSON cleaning functionality."""
    print("🧪 Testing JSON cleaning functionality...")
    
    # Create an extractor instance
    extractor = LangExtractExtractor(
        prompt_description="Test prompt",
        examples="Test examples",
        model_id="gemini-2.5-flash",
        passes=1
    )
    
    # Test case 1: Simple JSON with Docker log prefixes
    raw_json_1 = '''langextract-worker  | {
langextract-worker  |   "extractions": [
langextract-worker  |     {
langextract-worker  |       "product": "Test Product",
langextract-worker  |       "product_attributes": {
langextract-worker  |         "base_product_name": "Test"
langextract-worker  |       }
langextract-worker  |     }
langextract-worker  |   ]
langextract-worker  | }'''
    
    cleaned_1 = extractor._clean_docker_log_json(raw_json_1)
    print(f"✅ Test 1 - Cleaned JSON: {cleaned_1[:100]}...")
    
    try:
        parsed_1 = json.loads(cleaned_1)
        print("✅ Test 1 - JSON parsing successful")
    except json.JSONDecodeError as e:
        print(f"❌ Test 1 - JSON parsing failed: {e}")
        return False
    
    # Test case 2: Complex JSON with timestamps
    raw_json_2 = '''2025-08-18 19:25:42,950 - absl - DEBUG - Top inference result: {
langextract-worker  |   "extractions": [
langextract-worker  |     {
langextract-worker  |       "product": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
langextract-worker  |       "product_attributes": {
langextract-worker  |         "base_product_name": "502 FS CBD Oil",
langextract-worker  |         "option1_name": "Size",
langextract-worker  |         "option1_value": "1oz"
langextract-worker  |       }
langextract-worker  |     }
langextract-worker  |   ]
langextract-worker  | }'''
    
    cleaned_2 = extractor._clean_docker_log_json(raw_json_2)
    print(f"✅ Test 2 - Cleaned JSON: {cleaned_2[:100]}...")
    
    try:
        parsed_2 = json.loads(cleaned_2)
        print("✅ Test 2 - JSON parsing successful")
    except json.JSONDecodeError as e:
        print(f"❌ Test 2 - JSON parsing failed: {e}")
        return False
    
    return True

def test_balanced_json_extraction():
    """Test the balanced JSON block extraction."""
    print("\n🧪 Testing balanced JSON extraction...")
    
    extractor = LangExtractExtractor(
        prompt_description="Test prompt",
        examples="Test examples",
        model_id="gemini-2.5-flash",
        passes=1
    )
    
    # Test case: JSON with nested objects
    test_text = '''Some log text before
Top inference result: {
  "extractions": [
    {
      "product": "Test Product",
      "product_attributes": {
        "nested": {
          "deep": "value"
        }
      }
    }
  ]
}
Some log text after'''
    
    json_block = extractor._extract_balanced_json_block(test_text[test_text.find('{'):])
    if json_block:
        print(f"✅ Extracted balanced JSON: {json_block[:100]}...")
        try:
            parsed = json.loads(json_block)
            print("✅ Balanced JSON parsing successful")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ Balanced JSON parsing failed: {e}")
            return False
    else:
        print("❌ Failed to extract balanced JSON")
        return False

def test_top_inference_extraction():
    """Test the top inference result extraction from logs."""
    print("\n🧪 Testing top inference result extraction...")
    
    extractor = LangExtractExtractor(
        prompt_description="Test prompt",
        examples="Test examples",
        model_id="gemini-2.5-flash",
        passes=1
    )
    
    # Simulate log content with Top inference result
    log_content = '''2025-08-18 19:25:42,949 - absl - DEBUG - Top inference result: {
langextract-worker  |   "extractions": [
langextract-worker  |     {
langextract-worker  |       "product": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
langextract-worker  |       "product_attributes": {
langextract-worker  |         "base_product_name": "502 FS CBD Oil",
langextract-worker  |         "option1_name": "Size",
langextract-worker  |         "option1_value": "1oz",
langextract-worker  |         "option2_name": "Strength",
langextract-worker  |         "option2_value": "250mg",
langextract-worker  |         "option3_name": "Flavor",
langextract-worker  |         "option3_value": "Pink Lemonade"
langextract-worker  |       }
langextract-worker  |     }
langextract-worker  |   ]
langextract-worker  | }
2025-08-18 19:25:42,950 - absl - INFO - Starting resolver process for input text.'''
    
    result = extractor._extract_top_inference_result(log_content)
    if result:
        print("✅ Successfully extracted top inference result")
        print(f"✅ Found {len(result.get('extractions', []))} extractions")
        return True
    else:
        print("❌ Failed to extract top inference result")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting JSON parsing fix validation tests...\n")
    
    tests = [
        ("JSON Cleaning", test_json_cleaning),
        ("Balanced JSON Extraction", test_balanced_json_extraction),
        ("Top Inference Extraction", test_top_inference_extraction),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            failed += 1
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All JSON parsing fix tests passed!")
        print("\nKey improvements validated:")
        print("• ✅ Improved regex pattern for JSON extraction")
        print("• ✅ Enhanced Docker log cleaning")
        print("• ✅ Robust balanced JSON block extraction")
        print("• ✅ Better error handling in JSON parsing")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
