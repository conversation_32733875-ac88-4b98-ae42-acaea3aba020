# 🚀 Augment IDE Prompt — “LangExtract SQS Worker + FastAPI + Mongo + S3 (Revised)”

**Goal**: Extend an existing Python 3.12 project (variant extraction using LangExtract) with:

* A **CLI worker** that listens to AWS **SQS** (`extraction-queue`), fetches CSV from **S3**, streams/reads in **batches**, calls **LangExtract**, and persists results to **MongoDB**.
* A **FastAPI service** (with <PERSON>wagger) exposing:

  1. **Prompt APIs** (list categories, fetch category-specific prompt templates),
  2. **S3 simple pre-signed upload API** (single endpoint),
  3. **Extraction orchestration APIs** (create an extraction and enqueue a message to SQS **including the prompt text provided by the UI**).
* **docker-compose** to run `api`, `worker`, and `mongodb` locally.
* Production-ready concerns: batching, concurrency with a single replica, idempotency, retries, visibility timeout, logging, structured errors.

> The project already has a `main.py` that performs CSV → LangExtract → CSV/HTML outputs. Treat that logic as a **reusable module** (move core logic to `langextract_core/` and import from both worker and API where needed).

> Prompt category templates come from a provided prompt file (docx). Parse/encode these template definitions to JSON and serve via API. Maintain faithful wording and constraints.

---

## 1) Tech & Versions

* Python **3.12**
* **FastAPI** + **Uvicorn**
* **boto3** for S3 + SQS (you may use `aioboto3` for async S3 downloads; SQS receive can be sync or an async worker with a thread pool)
* **pymongo** (MongoDB at `mongodb://mongo:27017` in Docker, `localhost:27017` for local dev)
* **pydantic v2**
* **loguru** (structured logging) or stdlib logging with JSON formatter
* **python-dotenv**
* **tenacity** (retries)
* **orjson** (fast JSON)
* **pytest** for basic tests

---

## 2) Project Layout

```
langextract/
├── langextract_core/
│   ├── __init__.py
│   ├── extractor.py            # wraps existing LangExtract logic (moved from main.py)
│   ├── batching.py             # CSV streaming & batching utilities
│   ├── models.py               # pydantic models for inputs/outputs
│   ├── prompts_registry.py     # loads/serves prompt templates (seeded from docx -> JSON)
│   └── storage.py              # Mongo persistence functions
├── api/
│   ├── __init__.py
│   ├── main.py                 # FastAPI app + routers
│   ├── routers/
│   │   ├── prompts.py          # GET categories, GET prompt by category
│   │   ├── uploads.py          # single presign endpoint
│   │   └── extractions.py      # POST create extraction -> enqueue SQS
│   └── schemas/
│       ├── prompts.py
│       ├── uploads.py
│       └── extractions.py
├── worker/
│   ├── __init__.py
│   └── cli.py                  # SQS poller + per-message batch processing
├── infra/
│   ├── docker-compose.yml
│   ├── Dockerfile.api
│   ├── Dockerfile.worker
│   └── .env.example
├── scripts/
│   └── seed_prompts.py         # converts docx -> JSON and seeds Mongo or file
├── prompts/
│   └── categories.json         # normalized templates parsed from the docx
├── requirements.txt
└── README.md
```

---

## 3) Environment Variables (`infra/.env.example`)

```
# Mongo
MONGO_URI=mongodb://mongo:27017
MONGO_DB=langextract
MONGO_EXTRACT_COLLECTION=extractions
MONGO_RESULTS_COLLECTION=extraction_results
MONGO_PROMPTS_COLLECTION=prompts

# AWS
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your-bucket
AWS_SQS_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/123456789012/extraction-queue

# LangExtract
LANGEXTRACT_API_KEY=replace
LANGEXTRACT_MODEL_ID=gemini-2.5-flash

# Worker
WORKER_CONCURRENCY=4                 # messages processed concurrently
BATCH_SIZE=500                       # rows per batch
SQS_WAIT_TIME_SECONDS=20             # long poll
SQS_VISIBILITY_TIMEOUT=300           # seconds
MAX_RETRY_ATTEMPTS=5
```

---

## 4) Mongo Collections & Schemas

**extractions**

```json
{
  "_id": "ObjectId",
  "extraction_id": "uuid",
  "s3": { "bucket": "string", "key": "string" },
  "status": "created|running|completed|failed|partial",
  "created_at": "datetime",
  "updated_at": "datetime",
  "prompt": {
    "category": "string|null",
    "text": "string"                 // ← raw prompt text received from the UI
  },
  "model": { "id": "string", "passes": 1 },
  "column_name": "product_name",
  "metrics": { "total_rows": 0, "processed_rows": 0, "batches_total": 0, "batches_done": 0 },
  "errors": []
}
```

**extraction\_results** (one doc per processed row)

```json
{
  "_id": "ObjectId",
  "extraction_id": "uuid",
  "row_index": 123,
  "original": "string",
  "parsed": {
    "BaseProductName": "string",
    "Option1Name": "string", "Option1Value": "string",
    "Option2Name": "string", "Option2Value": "string",
    "Option3Name": "string|null", "Option3Value": "string|null"
  },
  "errors": []
}
```

**prompts** (seeded from the docx; no custom prompt storage)

```json
{
  "_id": "ObjectId",
  "category": "apparel|food_meat|cbd|paint|utensils_cutlery|...",
  "label": "Apparel — Color & Size",
  "prompt_description": "string",
  "schema_hint": { "max_options": 3 },
  "is_builtin": true
}
```

Indexes:

* `extractions.extraction_id` unique, `status`
* `extraction_results.extraction_id + row_index`
* `prompts.category` unique

---

## 5) SQS Message Contract

```json
{
  "type": "START_EXTRACTION",
  "extraction_id": "UUIDv4",
  "s3": { "bucket": "string", "key": "string" },
  "prompt": { "category": "string|null", "text": "string" },
  "model": { "id": "string", "passes": 1 },
  "column_name": "product_name"
}
```

* Message is **idempotent**: worker checks `extractions.status`. If already `running|completed`, skip or resume.
* Delete SQS message **only after** all batches processed & DB updated.

---

## 6) Worker Requirements (`worker/cli.py`)

* **CLI**: `python -m worker.cli` or entrypoint `langextract-worker`.
* **Long-poll SQS** with `WaitTimeSeconds=SQS_WAIT_TIME_SECONDS`, `MaxNumberOfMessages<=WORKER_CONCURRENCY`.
* **Concurrency**: process up to `WORKER_CONCURRENCY` messages **in parallel**; **within a single message**, process its file **batch-by-batch sequentially** (no interleaving of batches from different files). Use an asyncio `Semaphore` or thread pool to bound message parallelism.
* **Visibility timeout & heartbeat**: extend visibility if a batch run is long (change message visibility or re-poll with `VisibilityTimeout=SQS_VISIBILITY_TIMEOUT`).
* **Batching**:

  * Stream CSV from S3 using `boto3.get_object` + `StreamingBody.iter_lines()`; wrap with `io.TextIOWrapper` + `csv.DictReader`.
  * Accumulate rows list of size `BATCH_SIZE` → call `extractor.process_batch(rows, prompt_text, model)` → persist results.
* **Persistence**:

  * Upsert `extractions` on start: `status=running`, set `metrics`.
  * Insert many into `extraction_results` per batch.
  * On success: set `status=completed`, final metrics.
  * On errors: set `status=failed` with batch indices; do **not** delete SQS message so DLQ redrive can capture, or mark handled if DLQ isn’t configured.
* **Retries**:

  * Transient failures to LangExtract/S3/Mongo → `tenacity` exponential backoff, capped by `MAX_RETRY_ATTEMPTS`.
* **Logging**:

  * Structured JSON logs with fields: `extraction_id`, `batch`, `processed_rows`, elapsed time.

---

## 7) Extractor Module (`langextract_core/extractor.py`)

* **Refactor** the logic from existing `main.py` into a reusable class `ProductExtractor`.
* API surface:

```python
class ProductExtractor:
    def __init__(self, api_key: str, model_id: str, passes: int, prompt_description: str): ...
    def process_batch(self, rows: list[str]) -> list[dict]:
        """Returns per-row parsed dicts (BaseProductName, OptionXName/Value...)."""
```

* Respect the **prompt text** (provided by the UI) and allow category templates to be used as a base.

---

## 8) Prompt Templates (`langextract_core/prompts_registry.py`)

* Load built-in categories from `prompts/categories.json` created by parsing the provided docx. Provide helpers:

```python
def list_categories() -> list[dict]: ...

def get_prompt_by_category(category: str) -> dict | None: ...
```

* **No custom prompt storage endpoint**. The actual extraction will accept a raw prompt text from the UI.

---

## 9) FastAPI Endpoints

### a) Swagger

* Auto at `/docs` and `/redoc`.

### b) Prompts

* `GET /prompts/categories` → `[{ category, label }]`
* `GET /prompts/{category}` → `{ category, label, prompt_description }`

### c) S3 Upload (Single Presign Endpoint)

* `POST /uploads/presign`
  Body: `{ "file_name": "string", "content_type": "text/csv" }`
  Returns: `{ "key": "uploads/{uuid}-{file_name}", "url": "https://s3..." }`
  Notes:

  * Generate a **single pre-signed PUT URL** for direct browser upload. (Multipart endpoints **removed**.)
  * Accept an optional `bucket` query param or default to `AWS_S3_BUCKET`.

### d) Extractions

* `POST /extractions/start`
  Body:

  ```json
  {
    "s3_key": "uploads/..../file.csv",
    "bucket": "optional-override",
    "prompt": {
      "category": "apparel",        
      "text": "<RAW PROMPT FROM UI>"  
    },
    "model": { "id": "gemini-2.5-flash", "passes": 1 },
    "column_name": "product_name"
  }
  ```

  Behavior:

  * Creates an `extractions` doc with status `created`, stores the **raw prompt text**.
  * Generates `extraction_id` (UUIDv4).
  * **Sends SQS** message with the contract above (including `prompt.text`).
  * Returns `{ "extraction_id": "uuid", "status": "queued" }`.

* `GET /extractions/{extraction_id}` → current status + metrics

* `GET /extractions/{extraction_id}/results?offset=0&limit=100` → paginated results

* (Optional) `GET /extractions/{extraction_id}/download.csv` → export results as CSV

---

## 10) docker-compose (`infra/docker-compose.yml`)

* **mongo**:

  * image: mongo:7
  * expose `27017`, volume for data
* **api**:

  * build from `Dockerfile.api`
  * env file `.env`
  * depends\_on mongo
  * command: `uvicorn api.main:app --host 0.0.0.0 --port 8080 --reload`
  * ports: `8080:8080`
* **worker**:

  * build from `Dockerfile.worker`
  * env file `.env`
  * depends\_on mongo
  * command: `python -m worker.cli`

> Ensure containers share a network; API & worker reach `mongo` by hostname `mongo`.

---

## 11) Implementation Notes

* **Streaming CSV** from S3:

```python
obj = s3.get_object(Bucket=bucket, Key=key)
body = obj["Body"]
reader = csv.DictReader(io.TextIOWrapper(body, encoding="utf-8"))
for row in reader:
    # accumulate into batches using column_name
```

* **Batch extraction**: call `ProductExtractor.process_batch()` once per batch with the **raw prompt text**.
* **Idempotency**:

  * If `extractions.status in {"running","completed"}` → skip enqueue or handle resume.
  * Use an `extraction_id` unique index.
* **Error handling**:

  * Persist errors with batch index & line numbers.
  * On unrecoverable error → `status=failed`; leave message for DLQ redrive or mark handled if DLQ isn’t configured.
* **Metrics**: update `processed_rows`, `batches_done` after each batch, `updated_at` every N seconds.
* **Security**:

  * Do **not** accept arbitrary bucket unless authorized.
  * Redact secrets in logs.

---

## 12) Minimal Code Stubs (interfaces)

**worker/cli.py**

```python
def main():
    # init config, mongo, boto3 clients
    # loop: receive up to WORKER_CONCURRENCY messages (long poll)
    # spawn tasks (bounded) -> process_message(msg)
    # process_message: fetch extraction doc, set running; stream S3; for each batch -> extract -> persist; set completed; delete message
if __name__ == "__main__":
    main()
```

**api/routers/extractions.py**

```python
@router.post("/extractions/start")
def start_extraction(payload: StartExtraction):
    # create doc, include prompt.text from UI, send SQS message, return extraction_id + queued
```

**api/routers/uploads.py**

```python
@router.post("/uploads/presign")
def presign(file_name: str, content_type: str, bucket: str | None = None):
    # return { key, url }
```

**api/routers/prompts.py**

```python
@router.get("/prompts/categories")
def categories(): ...

@router.get("/prompts/{category}")
def get_prompt(category: str): ...
```

---

## 13) Acceptance Criteria

* **Worker**

  * Processes **N messages concurrently** (`WORKER_CONCURRENCY`), but each message’s file is handled **batch-sequentially**.
  * Batching works with large CSV (memory bounded).
  * Retries transient failures; respects visibility timeout; deletes message on success.
  * Updates `extractions.status` and metrics accurately.

* **API**

  * Swagger at `/docs` shows all endpoints & schemas.
  * **Only one** pre-sign endpoint is present; multipart initiate/parts/complete endpoints **removed**.
  * Prompt routes return categories and exact prompt text from the provided document templates; **no custom prompt storage**.
  * `POST /extractions/start` accepts **raw prompt text from the UI** and enqueues SQS with that prompt.

* **DevX**

  * `docker compose up --build` starts `mongo`, `api`, `worker`.
  * `.env.example` provided; README explains setup, seeding prompts via `scripts/seed_prompts.py`.

---

## 14) Bonus (Optional)

* `/healthz` (checks Mongo and SQS permissions).
* `/extractions/{id}/stats` with throughput and error counts.
* Simple API key auth for write routes.

---

**Build all of the above in Python 3.12, using best practices and clean, documented code.**
