"""
API schemas for extraction-related endpoints.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from langextract_core.models import ExtractionStatus, PromptInfo, ModelConfig


class StartExtractionRequest(BaseModel):
    """Request model for starting an extraction."""
    s3_key: str = Field(..., description="S3 object key for the CSV file")
    bucket: Optional[str] = Field(None, description="S3 bucket (optional override)")
    prompt: PromptInfo = Field(..., description="Prompt information")
    model: ModelConfig = Field(default_factory=ModelConfig, description="Model configuration")
    column_name: str = Field(default="product_name", description="CSV column name")


class StartExtractionResponse(BaseModel):
    """Response model for extraction start."""
    extraction_id: str = Field(..., description="Unique extraction identifier")
    status: str = Field(default="queued", description="Initial status")


class ExtractionStatusResponse(BaseModel):
    """Response model for extraction status."""
    extraction_id: str = Field(..., description="Extraction identifier")
    status: ExtractionStatus = Field(..., description="Current status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Processing metrics")
    errors: List[str] = Field(default_factory=list, description="Error messages")


class ExtractionResultItem(BaseModel):
    """Individual extraction result item."""
    row_index: int = Field(..., description="Row index in original CSV")
    original: str = Field(..., description="Original product name")
    parsed: Dict[str, Any] = Field(..., description="Parsed extraction result")
    errors: List[str] = Field(default_factory=list, description="Processing errors")


class ExtractionResultsResponse(BaseModel):
    """Response model for extraction results."""
    extraction_id: str = Field(..., description="Extraction identifier")
    results: List[ExtractionResultItem] = Field(..., description="Extraction results")
    total_count: int = Field(..., description="Total number of results")
    offset: int = Field(..., description="Current offset")
    limit: int = Field(..., description="Current limit")
