"""
API schemas for prompt-related endpoints.
"""

from typing import List, Dict, Any
from pydantic import BaseModel, Field


class PromptCategoryResponse(BaseModel):
    """Response model for prompt categories list."""
    category: str = Field(..., description="Category identifier")
    label: str = Field(..., description="Human-readable label")


class PromptTemplateResponse(BaseModel):
    """Response model for individual prompt template."""
    category: str = Field(..., description="Category identifier")
    label: str = Field(..., description="Human-readable label")
    prompt_description: str = Field(..., description="Full prompt text")
    schema_hint: Dict[str, Any] = Field(default_factory=dict, description="Schema hints")


class PromptCategoriesListResponse(BaseModel):
    """Response model for list of prompt categories."""
    categories: List[PromptCategoryResponse] = Field(..., description="List of available categories")
