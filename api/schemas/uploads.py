"""
API schemas for upload-related endpoints.
"""

from pydantic import BaseModel, Field, validator


class PresignRequest(BaseModel):
    """Request model for presigned URL generation."""
    file_name: str = Field(..., description="Name of the file to upload", min_length=1, max_length=255)
    content_type: str = Field(default="text/csv", description="MIME type of the file")

    @validator('file_name')
    def validate_file_name(cls, v):
        """Validate file name is not empty and has reasonable length."""
        if not v or not v.strip():
            raise ValueError('File name cannot be empty')
        return v.strip()

    @validator('content_type')
    def validate_content_type(cls, v):
        """Validate content type format."""
        if not v or '/' not in v:
            raise ValueError('Invalid content type format')
        return v


class PresignResponse(BaseModel):
    """Response model for presigned URL."""
    key: str = Field(..., description="S3 object key for the uploaded file")
    url: str = Field(..., description="Presigned URL for direct upload")
