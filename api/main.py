"""
FastAPI main application for LangExtract service.
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from api.routers import prompts, uploads, extractions
from langextract_core.storage import MongoStorage

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting LangExtract API service...")
    
    # Test MongoDB connection
    try:
        storage = MongoStorage()
        # Simple connection test
        categories = storage.get_prompt_categories()
        logger.info(f"MongoDB connected successfully. Found {len(categories)} prompt categories.")
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down LangExtract API service...")


# Create FastAPI application
app = FastAPI(
    title="LangExtract API",
    description="API service for product variant extraction using LangExtract",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(prompts.router)
app.include_router(uploads.router)
app.include_router(extractions.router)


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "LangExtract API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/healthz")
async def health_check():
    """Health check endpoint."""
    try:
        # Test MongoDB connection
        storage = MongoStorage()
        categories = storage.get_prompt_categories()
        
        # Test AWS configuration (without requiring broad permissions)
        import boto3
        try:
            # Just check if we can create clients with region - no API calls
            aws_region = os.getenv("AWS_REGION", "ap-south-1")
            s3_bucket = os.getenv("AWS_S3_BUCKET")
            sqs_queue_url = os.getenv("AWS_SQS_QUEUE_URL")

            # Create clients to verify configuration
            boto3.client('s3', region_name=aws_region)
            boto3.client('sqs', region_name=aws_region)

            # Check if required environment variables are set
            if s3_bucket and sqs_queue_url:
                aws_status = "configured"
            else:
                aws_status = "missing_config"

        except Exception as e:
            logger.warning(f"AWS configuration check failed: {e}")
            aws_status = "error"
        
        return {
            "status": "healthy",
            "mongodb": "connected",
            "prompt_categories": len(categories),
            "aws": {
                "status": aws_status,
                "region": os.getenv("AWS_REGION", "ap-south-1"),
                "s3_bucket_configured": bool(os.getenv("AWS_S3_BUCKET")),
                "sqs_queue_configured": bool(os.getenv("AWS_SQS_QUEUE_URL"))
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
