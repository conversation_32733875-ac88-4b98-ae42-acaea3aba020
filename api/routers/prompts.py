"""
API router for prompt-related endpoints.
"""

from typing import List
from fastapi import APIRouter, HTTPException, Depends
import logging

from langextract_core.storage import MongoStorage
from langextract_core.models import PromptTemplate
from api.schemas.prompts import PromptCategoriesListResponse, PromptCategoryResponse, PromptTemplateResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/prompts", tags=["prompts"])


def get_storage() -> MongoStorage:
    """Dependency to get MongoDB storage instance."""
    return MongoStorage()


@router.get("/categories", response_model=PromptCategoriesListResponse)
async def get_categories(storage: MongoStorage = Depends(get_storage)):
    """
    Get list of available prompt categories.
    
    Returns:
        List of prompt categories with identifiers and labels
    """
    try:
        categories_data = storage.get_prompt_categories()
        categories = [
            PromptCategoryResponse(category=cat["category"], label=cat["label"])
            for cat in categories_data
        ]
        
        return PromptCategoriesListResponse(categories=categories)
        
    except Exception as e:
        logger.error(f"Error getting prompt categories: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{category}", response_model=PromptTemplateResponse)
async def get_prompt(category: str, storage: MongoStorage = Depends(get_storage)):
    """
    Get prompt template by category.
    
    Args:
        category: Prompt category identifier
        
    Returns:
        Prompt template with full details
    """
    try:
        template = storage.get_prompt_by_category(category)
        
        if not template:
            raise HTTPException(status_code=404, detail=f"Prompt category '{category}' not found")
        
        return PromptTemplateResponse(
            category=template.category,
            label=template.label,
            prompt_description=template.prompt_description,
            schema_hint=template.schema_hint
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting prompt for category {category}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
