"""
API router for extraction-related endpoints.
"""

import os
import uuid
import json
from typing import List
from fastapi import APIRouter, HTTPException, Depends, Query
import boto3
from botocore.exceptions import ClientError
import logging

from langextract_core.storage import MongoStorage
from langextract_core.models import (
    ExtractionDocument, ExtractionStatus, S3Config, 
    PromptInfo, ModelConfig, SQSMessage
)
from api.schemas.extractions import (
    StartExtractionRequest, StartExtractionResponse,
    ExtractionStatusResponse, ExtractionResultsResponse, ExtractionResultItem
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/extractions", tags=["extractions"])


def get_storage() -> MongoStorage:
    """Dependency to get MongoDB storage instance."""
    return MongoStorage()


def get_sqs_client():
    """Get SQS client instance."""
    aws_region = os.getenv("AWS_REGION", "ap-south-1")
    return boto3.client('sqs', region_name=aws_region)


@router.post("/start", response_model=StartExtractionResponse)
async def start_extraction(
    request: StartExtractionRequest,
    storage: MongoStorage = Depends(get_storage)
):
    """
    Start a new extraction job by creating database record and enqueuing SQS message.
    
    Args:
        request: Extraction start request
        
    Returns:
        Extraction ID and initial status
    """
    try:
        # Generate unique extraction ID
        extraction_id = str(uuid.uuid4())
        
        # Use provided bucket or default from environment
        bucket = request.bucket or os.getenv("AWS_S3_BUCKET")
        if not bucket:
            raise HTTPException(status_code=400, detail="S3 bucket not specified")
        
        # Create extraction document
        extraction_doc = ExtractionDocument(
            extraction_id=extraction_id,
            s3=S3Config(bucket=bucket, key=request.s3_key),
            prompt=request.prompt,
            model=request.model,
            column_name=request.column_name,
            status=ExtractionStatus.CREATED
        )
        
        # Save to database
        created = storage.create_extraction(extraction_doc)
        if not created:
            raise HTTPException(status_code=409, detail="Extraction already exists")
        
        # Create SQS message
        sqs_message = SQSMessage(
            extraction_id=extraction_id,
            s3=S3Config(bucket=bucket, key=request.s3_key),
            prompt=request.prompt,
            model=request.model,
            column_name=request.column_name
        )
        
        # Send to SQS
        sqs_client = get_sqs_client()
        queue_url = os.getenv("AWS_SQS_QUEUE_URL")
        if not queue_url:
            raise HTTPException(status_code=500, detail="SQS queue URL not configured")
        
        sqs_client.send_message(
            QueueUrl=queue_url,
            MessageBody=json.dumps(sqs_message.model_dump())
        )
        
        logger.info(f"Started extraction {extraction_id} and enqueued SQS message")
        
        return StartExtractionResponse(
            extraction_id=extraction_id,
            status="queued"
        )
        
    except HTTPException:
        raise
    except ClientError as e:
        logger.error(f"AWS error starting extraction: {e}")
        raise HTTPException(status_code=500, detail="Failed to enqueue extraction job")
    except Exception as e:
        logger.error(f"Error starting extraction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{extraction_id}", response_model=ExtractionStatusResponse)
async def get_extraction_status(
    extraction_id: str,
    storage: MongoStorage = Depends(get_storage)
):
    """
    Get current status and metrics for an extraction.
    
    Args:
        extraction_id: Extraction identifier
        
    Returns:
        Current extraction status and metrics
    """
    try:
        extraction = storage.get_extraction(extraction_id)
        if not extraction:
            raise HTTPException(status_code=404, detail="Extraction not found")
        
        return ExtractionStatusResponse(
            extraction_id=extraction.extraction_id,
            status=extraction.status,
            created_at=extraction.created_at,
            updated_at=extraction.updated_at,
            metrics=extraction.metrics.model_dump(),
            errors=extraction.errors
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting extraction status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{extraction_id}/results", response_model=ExtractionResultsResponse)
async def get_extraction_results(
    extraction_id: str,
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum results to return"),
    storage: MongoStorage = Depends(get_storage)
):
    """
    Get paginated extraction results.
    
    Args:
        extraction_id: Extraction identifier
        offset: Number of results to skip
        limit: Maximum results to return
        
    Returns:
        Paginated extraction results
    """
    try:
        # Verify extraction exists
        extraction = storage.get_extraction(extraction_id)
        if not extraction:
            raise HTTPException(status_code=404, detail="Extraction not found")
        
        # Get results
        results = storage.get_extraction_results(extraction_id, offset, limit)
        
        # Convert to response format
        result_items = [
            ExtractionResultItem(
                row_index=result.row_index,
                original=result.original,
                parsed=result.parsed.model_dump(),
                errors=result.errors
            )
            for result in results
        ]
        
        return ExtractionResultsResponse(
            extraction_id=extraction_id,
            results=result_items,
            total_count=extraction.metrics.processed_rows,
            offset=offset,
            limit=limit
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting extraction results: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
