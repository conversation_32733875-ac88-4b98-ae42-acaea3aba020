"""
API router for S3 upload-related endpoints.
"""

import os
import uuid
import re
from urllib.parse import quote
from fastapi import APIRouter, HTTPException, Query
import boto3
from botocore.exceptions import ClientError
import logging

from api.schemas.uploads import PresignRequest, PresignResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/uploads", tags=["uploads"])


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe S3 key usage.

    Args:
        filename: Original filename

    Returns:
        Sanitized filename safe for S3 keys
    """
    if not filename:
        return "unnamed_file"

    # Remove or replace unsafe characters
    # Keep alphanumeric, dots, hyphens, underscores
    sanitized = re.sub(r'[^a-zA-Z0-9.\-_]', '_', filename)

    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)

    # Remove leading/trailing underscores and dots
    sanitized = sanitized.strip('_.')

    # Ensure it's not empty after sanitization
    if not sanitized:
        return "unnamed_file"

    # Limit length to reasonable size
    if len(sanitized) > 100:
        name_part, ext_part = os.path.splitext(sanitized)
        sanitized = name_part[:95] + ext_part

    return sanitized


def get_s3_client():
    """Get S3 client instance."""
    aws_region = os.getenv("AWS_REGION", "ap-south-1")
    return boto3.client('s3', region_name=aws_region)


@router.post("/presign", response_model=PresignResponse)
async def presign_upload(
    request: PresignRequest,
    bucket: str = Query(None, description="S3 bucket override")
):
    """
    Generate presigned URL for direct file upload to S3.
    
    Args:
        request: Presign request with file details
        bucket: Optional bucket override
        
    Returns:
        Presigned URL and S3 key for upload
    """
    try:
        # Get bucket from parameter or environment
        s3_bucket = bucket or os.getenv("AWS_S3_BUCKET")
        if not s3_bucket:
            raise HTTPException(status_code=400, detail="S3 bucket not specified")
        
        # Generate unique key for the file with sanitized filename
        file_uuid = str(uuid.uuid4())
        sanitized_filename = sanitize_filename(request.file_name)
        s3_key = f"uploads/{file_uuid}-{sanitized_filename}"
        
        # Create S3 client
        s3_client = get_s3_client()
        
        # Generate presigned URL for PUT operation
        presigned_url = s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': s3_bucket,
                'Key': s3_key,
                'ContentType': request.content_type
            },
            ExpiresIn=3600  # 1 hour expiration
        )
        
        logger.info(f"Generated presigned URL for {s3_key}")
        
        return PresignResponse(
            key=s3_key,
            url=presigned_url
        )
        
    except ClientError as e:
        logger.error(f"AWS S3 error generating presigned URL: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate upload URL")
    except Exception as e:
        logger.error(f"Error generating presigned URL: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
