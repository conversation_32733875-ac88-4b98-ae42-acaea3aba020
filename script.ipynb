!pip install langextract pandas

import langextract as lx
import textwrap
import csv
import os

# 1. Configure your API key (for Gemini/OpenAI models)
#    Either set the environment variable LANGEXTRACT_API_KEY,
#    or replace the os.environ.get(...) call below with your key string.
os.environ.setdefault("LANGEXTRACT_API_KEY", "api_key_here")


# 2. Define the prompt
prompt_description = textwrap.dedent("""\
    Extract the base product name and any option name/value pairs (such as color, size, material, model, etc.) 
    from each product name string. For each product, output a single extraction with attributes:
    base_product_name, option1_name, option1_value, option2_name, option2_value, etc.
""")

sample_data = [
    ("Classic Tee Blue M", {"base_product_name":"Classic Tee", "option1_name":"Color", "option1_value":"Blue", "option2_name":"Size", "option2_value":"M"}),
    ("Sneaker Airmax White 10 Men", {"base_product_name":"Sneaker Airmax", "option1_name":"Color", "option1_value":"White", "option2_name":"Size", "option2_value":"10", "option3_name":"Gender", "option3_value":"Men"}),
    ("Diamond Ring Platinum Ruby", {"base_product_name":"Diamond Ring", "option1_name":"Metal", "option1_value":"Platinum", "option2_name":"Stone", "option2_value":"Ruby"}),
    ("Hybrid Bike Large Red 18-speed", {"base_product_name":"Hybrid Bike", "option1_name":"Frame Size", "option1_value":"Large", "option2_name":"Color", "option2_value":"Red", "option3_name":"Gear Count", "option3_value":"18"}),
    ("Dress Shirt Slim White L", {"base_product_name":"Dress Shirt", "option1_name":"Fit", "option1_value":"Slim", "option2_name":"Color", "option2_value":"White", "option3_name":"Size", "option3_value":"L"}),
    ("Sofa 3-Seater Blue Velvet", {"base_product_name":"Sofa", "option1_name":"Seater", "option1_value":"3-Seater", "option2_name":"Color", "option2_value":"Blue", "option3_name":"Material", "option3_value":"Velvet"}),
    ("Perfume Blossom Eau de Parfum 50ml", {"base_product_name":"Perfume", "option1_name":"Fragrance", "option1_value":"Blossom", "option2_name":"Type", "option2_value":"Eau de Parfum", "option3_name":"Volume", "option3_value":"50ml"}),
    ("Yoga Mat Eco 8mm Purple", {"base_product_name":"Yoga Mat", "option1_name":"Material", "option1_value":"Eco", "option2_name":"Thickness", "option2_value":"8mm", "option3_name":"Color", "option3_value":"Purple"}),
    ("Laptop Pro 16GB 512GB 15-inch", {"base_product_name":"Laptop Pro", "option1_name":"RAM", "option1_value":"16GB", "option2_name":"Storage", "option2_value":"512GB", "option3_name":"Screen Size", "option3_value":"15-inch"}),
    ("Wireless Mouse Pro Black", {"base_product_name":"Wireless Mouse", "option1_name":"Model", "option1_value":"Pro", "option2_name":"Color", "option2_value":"Black"}),
    ("Smartwatch Sport Silver 44mm", {"base_product_name":"Smartwatch", "option1_name":"Model", "option1_value":"Sport", "option2_name":"Color", "option2_value":"Silver", "option3_name":"Size", "option3_value":"44mm"}),
    ("Candle Vanilla Scented 3x6", {"base_product_name":"Candle", "option1_name":"Fragrance", "option1_value":"Vanilla", "option2_name":"Type", "option2_value":"Scented", "option3_name":"Size", "option3_value":"3x6"}),
    ("Car Seat Cover Swift 2020 Black", {"base_product_name":"Car Seat Cover", "option1_name":"Model", "option1_value":"Swift", "option2_name":"Year", "option2_value":"2020", "option3_name":"Color", "option3_value":"Black"}),
    ("Bluetooth Speaker Portable 20W Black", {"base_product_name":"Bluetooth Speaker", "option1_name":"Feature", "option1_value":"Portable", "option2_name":"Power", "option2_value":"20W", "option3_name":"Color", "option3_value":"Black"}),
    ("Gaming Keyboard Mechanical RGB US-Layout", {"base_product_name":"Gaming Keyboard", "option1_name":"Type", "option1_value":"Mechanical", "option2_name":"Lighting", "option2_value":"RGB", "option3_name":"Layout", "option3_value":"US"}),
    ("Running Shoes Marathon 42 EU Blue", {"base_product_name":"Running Shoes", "option1_name":"Model", "option1_value":"Marathon", "option2_name":"Size", "option2_value":"42 EU", "option3_name":"Color", "option3_value":"Blue"}),
    ("Winter Jacket Down-filled Gray XL", {"base_product_name":"Winter Jacket", "option1_name":"Filling", "option1_value":"Down-filled", "option2_name":"Color", "option2_value":"Gray", "option3_name":"Size", "option3_value":"XL"}),
    ("Coffee Maker Single-Serve 250ml Red", {"base_product_name":"Coffee Maker", "option1_name":"Type", "option1_value":"Single-Serve", "option2_name":"Capacity", "option2_value":"250ml", "option3_name":"Color", "option3_value":"Red"}),
    ("Vacuum Cleaner Bagless 1500W", {"base_product_name":"Vacuum Cleaner", "option1_name":"Type", "option1_value":"Bagless", "option2_name":"Power", "option2_value":"1500W"}),
    ("Office Chair Ergonomic Mesh Black", {"base_product_name":"Office Chair", "option1_name":"Style", "option1_value":"Ergonomic", "option2_name":"Material", "option2_value":"Mesh", "option3_name":"Color", "option3_value":"Black"}),
    ("4K Monitor 27-inch IPS", {"base_product_name":"4K Monitor", "option1_name":"Size", "option1_value":"27-inch", "option2_name":"Panel", "option2_value":"IPS"}),
    ("External Hard Drive 1TB USB-C", {"base_product_name":"External Hard Drive", "option1_name":"Capacity", "option1_value":"1TB", "option2_name":"Interface", "option2_value":"USB-C"}),
    ("Action Camera 4K 30fps Waterproof", {"base_product_name":"Action Camera", "option1_name":"Resolution", "option1_value":"4K", "option2_name":"Frame Rate", "option2_value":"30fps", "option3_name":"Feature", "option3_value":"Waterproof"}),
    ("Electric Toothbrush Rechargeable White", {"base_product_name":"Electric Toothbrush", "option1_name":"Type", "option1_value":"Rechargeable", "option2_name":"Color", "option2_value":"White"}),
    ("Water Bottle Stainless Steel 500ml", {"base_product_name":"Water Bottle", "option1_name":"Material", "option1_value":"Stainless Steel", "option2_name":"Capacity", "option2_value":"500ml"}),
    ("Backpack Hiking 40L Green", {"base_product_name":"Backpack", "option1_name":"Use Case", "option1_value":"Hiking", "option2_name":"Capacity", "option2_value":"40L", "option3_name":"Color", "option3_value":"Green"}),
    ("Wireless Earbuds Noise-Cancelling Black", {"base_product_name":"Wireless Earbuds", "option1_name":"Feature", "option1_value":"Noise-Cancelling", "option2_name":"Color", "option2_value":"Black"}),
    ("Smartphone ModelX 128GB Midnight", {"base_product_name":"Smartphone", "option1_name":"Model", "option1_value":"ModelX", "option2_name":"Storage", "option2_value":"128GB", "option3_name":"Color", "option3_value":"Midnight"}),
    ("Tablet S10 10-inch WiFi", {"base_product_name":"Tablet", "option1_name":"Series", "option1_value":"S10", "option2_name":"Size", "option2_value":"10-inch", "option3_name":"Connectivity", "option3_value":"WiFi"}),
    ("Desk Lamp LED Dimmable", {"base_product_name":"Desk Lamp", "option1_name":"Light Type", "option1_value":"LED", "option2_name":"Feature", "option2_value":"Dimmable"}),
    ("Fitness Tracker HR Gold", {"base_product_name":"Fitness Tracker", "option1_name":"Feature", "option1_value":"HR", "option2_name":"Color", "option2_value":"Gold"}),
    ("Gaming Console NextGen Matte Black", {"base_product_name":"Gaming Console", "option1_name":"Version", "option1_value":"NextGen", "option2_name":"Finish", "option2_value":"Matte", "option3_name":"Color", "option3_value":"Black"}),
    ("Router Dual-Band AC1200", {"base_product_name":"Router", "option1_name":"Bands", "option1_value":"Dual-Band", "option2_name":"Standard", "option2_value":"AC1200"}),
    ("Projector Home 1080p 2000lumens", {"base_product_name":"Projector", "option1_name":"Use Case", "option1_value":"Home", "option2_name":"Resolution", "option2_value":"1080p", "option3_name":"Brightness", "option3_value":"2000lumens"}),
    ("Photo Frame Digital 10-inch", {"base_product_name":"Photo Frame", "option1_name":"Type", "option1_value":"Digital", "option2_name":"Size", "option2_value":"10-inch"}),
    ("Air Purifier HEPA Filter 50sqm", {"base_product_name":"Air Purifier", "option1_name":"Filter Type", "option1_value":"HEPA", "option2_name":"Coverage", "option2_value":"50sqm"}),
    ("Stand Mixer 7L Red", {"base_product_name":"Stand Mixer", "option1_name":"Capacity", "option1_value":"7L", "option2_name":"Color", "option2_value":"Red"}),
    ("Blender Smoothie 1200W White", {"base_product_name":"Blender", "option1_name":"Use Case", "option1_value":"Smoothie", "option2_name":"Power", "option2_value":"1200W", "option3_name":"Color", "option3_value":"White"}),
    ("Rice Cooker 5 Cups", {"base_product_name":"Rice Cooker", "option1_name":"Capacity", "option1_value":"5 Cups"}),
    ("Electric Kettle 1.7L Stainless Steel", {"base_product_name":"Electric Kettle", "option1_name":"Capacity", "option1_value":"1.7L", "option2_name":"Material", "option2_value":"Stainless Steel"}),
    ("Slow Cooker 6Quart Programmable", {"base_product_name":"Slow Cooker", "option1_name":"Capacity", "option1_value":"6Quart", "option2_name":"Feature", "option2_value":"Programmable"}),
    ("Espresso Machine Automatic 15bar", {"base_product_name":"Espresso Machine", "option1_name":"Operation", "option1_value":"Automatic", "option2_name":"Pressure", "option2_value":"15bar"}),
    ("Juicer Cold-Press 300W Silver", {"base_product_name":"Juicer", "option1_name":"Type", "option1_value":"Cold-Press", "option2_name":"Power", "option2_value":"300W", "option3_name":"Color", "option3_value":"Silver"}),
    ("Food Processor 12-Cup", {"base_product_name":"Food Processor", "option1_name":"Capacity", "option1_value":"12-Cup"}),
    ("Garden Hose 50ft Expandable Green", {"base_product_name":"Garden Hose", "option1_name":"Length", "option1_value":"50ft", "option2_name":"Feature", "option2_value":"Expandable", "option3_name":"Color", "option3_value":"Green"}),
    ("Power Bank 20000mAh USB-C", {"base_product_name":"Power Bank", "option1_name":"Capacity", "option1_value":"20000mAh", "option2_name":"Port", "option2_value":"USB-C"}),
    ("Photo Camera DSLR 24MP Body Only", {"base_product_name":"Photo Camera", "option1_name":"Type", "option1_value":"DSLR", "option2_name":"Resolution", "option2_value":"24MP", "option3_name":"Kit", "option3_value":"Body Only"}),
    ("Tripod Aluminum 60-inch Black", {"base_product_name":"Tripod", "option1_name":"Material", "option1_value":"Aluminum", "option2_name":"Max Height", "option2_value":"60-inch", "option3_name":"Color", "option3_value":"Black"}),
    ("Wireless Charger Fast-Charge 10W", {"base_product_name":"Wireless Charger", "option1_name":"Feature", "option1_value":"Fast-Charge", "option2_name":"Power", "option2_value":"10W"}),
]

# Build the ExampleData list
examples = [
    lx.data.ExampleData(
        text=name,
        extractions=[
            lx.data.Extraction(
                extraction_class="product",
                extraction_text=name,
                attributes=attrs
            )
        ]
    )
    for name, attrs in sample_data
]

# 4. List the product names you want to process
product_names = [
    "Over-Ear Noise Cancelling Headphones Wireless Matte Black",
    "4K UHD Smart TV 55-inch HDR Dolby Vision",
    "Running Shorts Dry-Fit Medium Blue",
    "Organic Cotton Bed Sheet King Size White",
    "Men's Leather Wallet RFID Blocking Brown",
    "Women's Waterproof Hiking Boots Size 8",
    "Gaming Mouse Wireless RGB 16000 DPI",
    "Smart LED Light Bulb 9W WiFi Color-Changing",
    "Electric Scooter 350W 20mph Black",
    "Solar Power Bank 10000mAh Dual USB",
    "Stainless Steel Cookware Set 10-Piece",
    "Bluetooth Car Kit Hands-Free Black",
    "Portable Projector 1080p 2000 ANSI Lumens",
    "Digital Alarm Clock LED Snooze Function",
    "Ceramic Vase Hand-Painted Blue and White",
    "Inflatable Kayak 2-Person Green",
    "Kids' Bike 16-inch Training Wheels Pink",
    "Cordless Vacuum Cleaner Lithium-Ion 25V",
    "Adjustable Office Desk Standing Sit-Stand",
    "Camping Tent 4-Person Dome Waterproof",
]


# 5. Run the extraction
result = lx.extract(
    text_or_documents="\n".join(product_names),
    prompt_description=prompt_description,
    examples=examples,
    model_id="gemini-2.5-flash",   # or your preferred model
    extraction_passes=1
)

# 6. Post-process the extractions into CSV rows
rows = []
max_options = 0

for ext in result.extractions:
    attrs = ext.attributes or {}
    row = {
        "OriginalProductName": ext.extraction_text,
        "BaseProductName": attrs.get("base_product_name", "")
    }
    # gather option pairs
    i = 1
    while True:
        name_key = f"option{i}_name"
        value_key = f"option{i}_value"
        if name_key in attrs and value_key in attrs:
            row[f"Option{i}Name"] = attrs[name_key]
            row[f"Option{i}Value"] = attrs[value_key]
            i += 1
        else:
            break
    max_options = max(max_options, i - 1)
    rows.append(row)

# 7. Prepare CSV header
header = ["OriginalProductName", "BaseProductName"]
for i in range(1, max_options + 1):
    header += [f"Option{i}Name", f"Option{i}Value"]

# 8. Print results in CSV format
# Print header row
print(",".join(header))

# Print each extracted row
for row in rows:
    print(",".join([row.get(col, "") for col in header]))

# save results in html
with open("results.html", "w") as f:
    f.write(result.html_report())
