#!/usr/bin/env python3
"""
Production-ready LangExtract Product Variant Extraction Application

This application processes product names from CSV files and extracts
base product names and option variants using LangExtract.
"""

import os
import sys
import csv
import logging
import textwrap
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse

import langextract as lx
import pandas as pd
from dotenv import load_dotenv


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('langextract_app.log')
    ]
)
logger = logging.getLogger(__name__)


class BatchResultManager:
    """Manages incremental collection and storage of batch processing results."""

    def __init__(self):
        self.batch_results = []
        self.failed_batches = []
        self.processing_stats = {
            'total_batches': 0,
            'completed_batches': 0,
            'failed_batches': 0,
            'total_extractions': 0,
            'processing_start_time': None,
            'last_batch_time': None
        }

    def start_processing(self, total_batches: int):
        """Initialize processing statistics."""
        self.processing_stats['total_batches'] = total_batches
        self.processing_stats['processing_start_time'] = time.time()
        logger.info(f"Starting batch processing: {total_batches} batches total")

    def add_batch_result(self, batch_num: int, extractions: List, processing_time: float):
        """Add successful batch result."""
        self.batch_results.append({
            'batch_num': batch_num,
            'extractions': extractions,
            'count': len(extractions),
            'processing_time': processing_time
        })
        self.processing_stats['completed_batches'] += 1
        self.processing_stats['total_extractions'] += len(extractions)
        self.processing_stats['last_batch_time'] = time.time()

        # Log progress
        progress = (self.processing_stats['completed_batches'] / self.processing_stats['total_batches']) * 100
        logger.info(f"Batch {batch_num} completed: {len(extractions)} extractions in {processing_time:.1f}s (Progress: {progress:.1f}%)")

    def add_batch_failure(self, batch_num: int, error: str):
        """Record failed batch."""
        self.failed_batches.append({
            'batch_num': batch_num,
            'error': error,
            'timestamp': time.time()
        })
        self.processing_stats['failed_batches'] += 1
        logger.error(f"Batch {batch_num} failed: {error}")

    def get_all_extractions(self) -> List:
        """Get all extractions from successful batches."""
        all_extractions = []
        for batch_result in self.batch_results:
            all_extractions.extend(batch_result['extractions'])
        return all_extractions

    def get_processing_summary(self) -> Dict[str, Any]:
        """Get comprehensive processing summary."""
        if self.processing_stats['processing_start_time']:
            total_time = time.time() - self.processing_stats['processing_start_time']
        else:
            total_time = 0

        return {
            'total_batches': self.processing_stats['total_batches'],
            'successful_batches': self.processing_stats['completed_batches'],
            'failed_batches': self.processing_stats['failed_batches'],
            'total_extractions': self.processing_stats['total_extractions'],
            'total_processing_time': total_time,
            'average_batch_time': total_time / max(1, self.processing_stats['completed_batches']),
            'success_rate': (self.processing_stats['completed_batches'] / max(1, self.processing_stats['total_batches'])) * 100,
            'failed_batch_numbers': [f['batch_num'] for f in self.failed_batches]
        }


class ProductExtractor:
    """Main class for product variant extraction using LangExtract."""

    def __init__(self, api_key: str, model_id: str = "gemini-2.5-flash", enable_html_output: bool = False):
        """
        Initialize the ProductExtractor.

        Args:
            api_key: API key for the LangExtract service
            model_id: Model ID to use for extraction
            enable_html_output: Whether to generate HTML visualization output
        """
        self.api_key = api_key
        self.model_id = model_id
        self.enable_html_output = enable_html_output
        self.prompt_description = textwrap.dedent("""\
            You are extracting variants strictly for a CBD catalog.

OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):
- base_product_name : string (original product name with Size/Strength/Flavor removed)
- option1_name      : literal "Size"
- option1_value     : string (e.g., "Small", "Medium", "Large", "30ml", "60ml", "30ct") or "" if absent
- option2_name      : literal "Strength"
- option2_value     : string (e.g., "25mg", "50mg", "100mg", "500mg", "1000mg", "25mg/mL") or "" if absent
- option3_name      : literal "Flavor"
- option3_value     : string (e.g., "Natural", "Spearmint", "Cherry", "Strawberry", "Sweet Leaf") or "" if absent

STRICT ATTRIBUTE WHITELIST:
Only emit these seven attributes: base_product_name, option1_name, option1_value, option2_name, option2_value, option3_name, option3_value.
Do NOT emit any other attributes or options.

HARD CONSTRAINTS:
1) Extract ONLY Size, Strength, and Flavor. No other variant/attribute is allowed.
2) If any of the three is missing/ambiguous, leave its value blank ("").
3) Use EXACT substrings from the input; do not paraphrase, normalize, or convert units.
4) Sizes: accept common descriptors (Small/Medium/Large) and literal pack/volume indicators if present as tokens (e.g., "30ml", "60ml", "30ct").
5) Strengths: accept literal potency tokens such as "25mg", "50mg", "100mg", "500mg", "1000mg", "25mg/mL" as they appear.
6) Flavors: accept single- or multi-word flavors, including hyphenated or ampersand forms (e.g., "Sweet Leaf", "Cherry", "Natural", "Spearmint", "Strawberry").
7) Keep brand/form/quality descriptors INSIDE base_product_name (e.g., "Full Spectrum", "Broad Spectrum", "THC-Free", "Isolate", "Gummies", "Tincture", "Capsules", "Pet", "Sleep", "Calm").
8) If a flavor word appears as part of a longer brand/word (e.g., "MintyFreshX"), do NOT treat it as Flavor unless it is a standalone token/phrase.
9) Hyphen/paren patterns: if a variant is hyphen/paren-attached (e.g., "Gummies - Spearmint", "(1000mg)"), still capture the exact variant text and conceptually remove it from base_product_name.
10) Never output any Option4/extra keys. Any key beyond the seven listed makes the output invalid.

OUTPUT RULES:
- option1_name must be "Size"; option2_name must be "Strength"; option3_name must be "Flavor".
- If only one or two variants are present, leave the others as "".
- base_product_name must preserve original wording/order minus the extracted Size/Strength/Flavor tokens.
- If ambiguous, leave the respective option value blank ("") instead of guessing.

Before finalizing, VALIDATE your output strictly uses ONLY the seven allowed attribute keys and contains no other attributes.
        """)

        # Set up the API key
        os.environ["LANGEXTRACT_API_KEY"] = self.api_key

        # Initialize example data for training
        self.examples = self._create_example_data()

        # Store the last extraction result for visualization
        self.last_extraction_result = None

        # Batch result manager for incremental processing
        self.batch_manager = BatchResultManager()

        # Statistics for data quality reporting
        self.processing_stats = {
            'total_input': 0,
            'after_cleaning': 0,
            'successfully_processed': 0,
            'failed_batches': 0
        }
        
    def _create_example_data(self) -> List[lx.data.ExampleData]:
        """Create example data for training the extraction model."""
        sample_data = [
    # Strength + Flavor (no explicit size)
    # Strength + Flavor (no explicit size)
    ("Full Spectrum CBD Gummies 25mg Cherry",
     {"base_product_name":"Full Spectrum CBD Gummies",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"25mg",
      "option3_name":"Flavor","option3_value":"Cherry"}),

    # Strength in parens, Flavor after dash
    ("CBD Oil Tincture (1000mg) - Spearmint",
     {"base_product_name":"CBD Oil Tincture",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"1000mg",
      "option3_name":"Flavor","option3_value":"Spearmint"}),

    # Size as volume, Strength as mg/mL, Flavor natural
    ("Broad Spectrum CBD Tincture 30ml 25mg/mL Natural",
     {"base_product_name":"Broad Spectrum CBD Tincture",
      "option1_name":"Size","option1_value":"30ml",
      "option2_name":"Strength","option2_value":"25mg/mL",
      "option3_name":"Flavor","option3_value":"Natural"}),

    # Size as count, Strength token only
    ("CBD Softgels 30ct 50mg",
     {"base_product_name":"CBD Softgels",
      "option1_name":"Size","option1_value":"30ct",
      "option2_name":"Strength","option2_value":"50mg",
      "option3_name":"Flavor","option3_value":""}),

    # Explicit Size word + Strength + Flavor
    ("THC-Free CBD Gummies Large 25mg Strawberry",
     {"base_product_name":"THC-Free CBD Gummies",
      "option1_name":"Size","option1_value":"Large",
      "option2_name":"Strength","option2_value":"25mg",
      "option3_name":"Flavor","option3_value":"Strawberry"}),

    # Medium size before form, hyphenated flavor
    ("CBD Capsules Medium - Sweet Leaf 100mg",
     {"base_product_name":"CBD Capsules",
      "option1_name":"Size","option1_value":"Medium",
      "option2_name":"Strength","option2_value":"100mg",
      "option3_name":"Flavor","option3_value":"Sweet Leaf"}),

    # Small size + Flavor; Strength absent
    ("Pet CBD Tincture Small Natural",
     {"base_product_name":"Pet CBD Tincture",
      "option1_name":"Size","option1_value":"Small",
      "option2_name":"Strength","option2_value":"",
      "option3_name":"Flavor","option3_value":"Natural"}),

    # Volume size + flavor; strength absent
    ("CBD Vape Juice 60ml Spearmint",
     {"base_product_name":"CBD Vape Juice",
      "option1_name":"Size","option1_value":"60ml",
      "option2_name":"Strength","option2_value":"",
      "option3_name":"Flavor","option3_value":"Spearmint"}),

    # Gummies with both flavor and strength; size not present
    ("Sleep CBD Gummies 50mg Strawberry",
     {"base_product_name":"Sleep CBD Gummies",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"50mg",
      "option3_name":"Flavor","option3_value":"Strawberry"}),

    # Count size + strength + flavor order mixed
    ("Calm CBD Gummies Spearmint 30ct 25mg",
     {"base_product_name":"Calm CBD Gummies",
      "option1_name":"Size","option1_value":"30ct",
      "option2_name":"Strength","option2_value":"25mg",
      "option3_name":"Flavor","option3_value":"Spearmint"}),

    # Volume size + high strength + flavor
    ("Full Spectrum CBD Oil 60ml 1000mg Cherry",
     {"base_product_name":"Full Spectrum CBD Oil",
      "option1_name":"Size","option1_value":"60ml",
      "option2_name":"Strength","option2_value":"1000mg",
      "option3_name":"Flavor","option3_value":"Cherry"}),

    # Broad spectrum with hyphen; only flavor present
    ("Broad-Spectrum CBD Drops Natural",
     {"base_product_name":"Broad-Spectrum CBD Drops",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"",
      "option3_name":"Flavor","option3_value":"Natural"}),

    # Capsules with size word and strength
    ("CBD Capsules Large 100mg",
     {"base_product_name":"CBD Capsules",
      "option1_name":"Size","option1_value":"Large",
      "option2_name":"Strength","option2_value":"100mg",
      "option3_name":"Flavor","option3_value":""}),

    # Pet line with flavor only
    ("Pet CBD Chews Spearmint",
     {"base_product_name":"Pet CBD Chews",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"",
      "option3_name":"Flavor","option3_value":"Spearmint"}),

    # Form + count size + flavor
    ("CBD Gummies 60ct Cherry",
     {"base_product_name":"CBD Gummies",
      "option1_name":"Size","option1_value":"60ct",
      "option2_name":"Strength","option2_value":"",
      "option3_name":"Flavor","option3_value":"Cherry"}),

    # Volume + mg strength; no flavor
    ("Isolate CBD Oil 30ml 500mg",
     {"base_product_name":"Isolate CBD Oil",
      "option1_name":"Size","option1_value":"30ml",
      "option2_name":"Strength","option2_value":"500mg",
      "option3_name":"Flavor","option3_value":""}),

    # Ampersand flavor; strength present; size missing
    ("CBD Tincture 25mg Sweet Leaf",
     {"base_product_name":"CBD Tincture",
      "option1_name":"Size","option1_value":"",
      "option2_name":"Strength","option2_value":"25mg",
      "option3_name":"Flavor","option3_value":"Sweet Leaf"}),

    # Everything present, different order
    ("CBD Gummies Spearmint Large 50mg",
     {"base_product_name":"CBD Gummies",
      "option1_name":"Size","option1_value":"Large",
      "option2_name":"Strength","option2_value":"50mg",
      "option3_name":"Flavor","option3_value":"Spearmint"}),
]


        
        return [
            lx.data.ExampleData(
                text=name,
                extractions=[
                    lx.data.Extraction(
                        extraction_class="product",
                        extraction_text=name,
                        attributes=attrs
                    )
                ]
            )
            for name, attrs in sample_data
        ]

    def validate_and_report_data_quality(self, product_names: List[str]) -> Dict[str, Any]:
        """
        Validate data quality and generate a comprehensive report.

        Args:
            product_names: List of product names to validate

        Returns:
            Dictionary containing data quality metrics and issues
        """
        report = {
            'total_products': len(product_names),
            'valid_products': 0,
            'issues': {
                'empty_or_whitespace': 0,
                'too_short': 0,
                'too_long': 0,
                'suspicious_patterns': 0,
                'duplicates': 0
            },
            'length_stats': {
                'min_length': float('inf'),
                'max_length': 0,
                'avg_length': 0
            },
            'sample_issues': []
        }

        if not product_names:
            return report

        seen_products = set()
        valid_products = []
        lengths = []

        for i, name in enumerate(product_names):
            # Check for empty or whitespace-only
            if not name or not name.strip():
                report['issues']['empty_or_whitespace'] += 1
                report['sample_issues'].append(f"Row {i+1}: Empty or whitespace-only")
                continue

            name = name.strip()

            # Check for too short
            if len(name) < 2:
                report['issues']['too_short'] += 1
                report['sample_issues'].append(f"Row {i+1}: Too short ('{name}')")
                continue

            # Check for too long
            if len(name) > 500:
                report['issues']['too_long'] += 1
                report['sample_issues'].append(f"Row {i+1}: Too long ({len(name)} chars)")
                continue

            # Check for suspicious patterns
            suspicious_patterns = ['test', 'sample', 'example', 'placeholder', 'dummy']
            if any(pattern in name.lower() for pattern in suspicious_patterns):
                report['issues']['suspicious_patterns'] += 1
                if len(report['sample_issues']) < 10:  # Limit sample issues
                    report['sample_issues'].append(f"Row {i+1}: Suspicious pattern ('{name}')")

            # Check for duplicates
            if name.lower() in seen_products:
                report['issues']['duplicates'] += 1
                if len(report['sample_issues']) < 10:
                    report['sample_issues'].append(f"Row {i+1}: Duplicate ('{name}')")
            else:
                seen_products.add(name.lower())
                valid_products.append(name)
                lengths.append(len(name))

        report['valid_products'] = len(valid_products)

        if lengths:
            report['length_stats'] = {
                'min_length': min(lengths),
                'max_length': max(lengths),
                'avg_length': sum(lengths) / len(lengths)
            }

        return report
    
    def read_product_names_from_csv(self, csv_file_path: str, column_name: str = "product_name") -> List[str]:
        """
        Read product names from a CSV file with comprehensive data cleaning.

        Args:
            csv_file_path: Path to the CSV file
            column_name: Name of the column containing product names

        Returns:
            List of cleaned product names
        """
        try:
            df = pd.read_csv(csv_file_path)
            if column_name not in df.columns:
                raise ValueError(f"Column '{column_name}' not found in CSV. Available columns: {list(df.columns)}")

            logger.info(f"Raw CSV contains {len(df)} rows")

            # Initial data cleaning
            product_series = df[column_name].dropna()  # Remove NaN values
            logger.info(f"After removing NaN values: {len(product_series)} rows")

            # Convert to string and strip whitespace
            product_series = product_series.astype(str).str.strip()

            # Remove empty strings and whitespace-only entries
            product_series = product_series[product_series != '']
            logger.info(f"After removing empty strings: {len(product_series)} rows")

            # Remove entries that are just whitespace or common placeholder values
            placeholder_patterns = ['nan', 'null', 'none', 'n/a', 'na', 'undefined', 'empty']
            product_series = product_series[~product_series.str.lower().isin(placeholder_patterns)]
            logger.info(f"After removing placeholder values: {len(product_series)} rows")

            # Remove entries that are too short (less than 2 characters) or too long (more than 500 characters)
            original_count = len(product_series)
            product_series = product_series[(product_series.str.len() >= 2) & (product_series.str.len() <= 500)]
            if len(product_series) < original_count:
                logger.info(f"Filtered out {original_count - len(product_series)} entries due to length constraints")

            # Convert to list and remove duplicates while preserving order
            product_names = list(dict.fromkeys(product_series.tolist()))

            if len(product_names) != len(product_series):
                logger.info(f"Removed {len(product_series) - len(product_names)} duplicate entries")

            logger.info(f"Successfully cleaned and loaded {len(product_names)} unique product names from {csv_file_path}")

            # Generate data quality report
            quality_report = self.validate_and_report_data_quality(product_names)

            # Log data quality summary
            logger.info(f"Data Quality Report:")
            logger.info(f"  Total products: {quality_report['total_products']}")
            logger.info(f"  Valid products: {quality_report['valid_products']}")
            logger.info(f"  Issues found: {sum(quality_report['issues'].values())}")

            if quality_report['issues']['empty_or_whitespace'] > 0:
                logger.warning(f"  - Empty/whitespace entries: {quality_report['issues']['empty_or_whitespace']}")
            if quality_report['issues']['too_short'] > 0:
                logger.warning(f"  - Too short entries: {quality_report['issues']['too_short']}")
            if quality_report['issues']['too_long'] > 0:
                logger.warning(f"  - Too long entries: {quality_report['issues']['too_long']}")
            if quality_report['issues']['duplicates'] > 0:
                logger.warning(f"  - Duplicate entries: {quality_report['issues']['duplicates']}")
            if quality_report['issues']['suspicious_patterns'] > 0:
                logger.warning(f"  - Suspicious patterns: {quality_report['issues']['suspicious_patterns']}")

            # Log sample issues if any
            if quality_report['sample_issues']:
                logger.warning("Sample data issues:")
                for issue in quality_report['sample_issues'][:5]:  # Show first 5 issues
                    logger.warning(f"  {issue}")

            # Log length statistics
            if quality_report['length_stats']['avg_length'] > 0:
                logger.info(f"Product name length stats:")
                logger.info(f"  Min: {quality_report['length_stats']['min_length']} chars")
                logger.info(f"  Max: {quality_report['length_stats']['max_length']} chars")
                logger.info(f"  Avg: {quality_report['length_stats']['avg_length']:.1f} chars")

            # Log some sample entries for verification
            if product_names:
                logger.info(f"Sample product names: {product_names[:3]}")

            # Update processing stats
            self.processing_stats['total_input'] = len(df)
            self.processing_stats['after_cleaning'] = len(product_names)

            return product_names

        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file_path}: {str(e)}")
            raise
    
    def _calculate_optimal_batch_size(self, product_names: List[str], override_batch_size: int = 0) -> int:
        """
        Calculate optimal batch size based on dataset characteristics and memory constraints.

        Args:
            product_names: List of product names
            override_batch_size: Manual override for batch size (0 = auto-calculate)

        Returns:
            Optimal batch size for processing
        """
        # Use override if provided
        if override_batch_size > 0:
            return min(override_batch_size, len(product_names))

        total_products = len(product_names)
        avg_length = sum(len(name) for name in product_names) / total_products if product_names else 0

        # Calculate total character count for memory estimation
        total_chars = sum(len(name) for name in product_names)

        # Dynamic batch sizing based on dataset size and characteristics
        if total_products <= 50:
            return total_products  # Process all at once for small datasets
        elif total_products <= 500:
            # Medium datasets: balance between efficiency and memory
            if avg_length > 100:
                return 25  # Smaller batches for long product names
            else:
                return 50
        elif total_products <= 10000:
            # Large datasets: prioritize memory efficiency
            if avg_length > 80:
                return 20  # Very small batches for long names
            elif avg_length > 50:
                return 30
            else:
                return 40
        else:
            # Very large datasets (100k+): ultra-conservative batching
            if avg_length > 60:
                batch_size = 15  # Ultra-small batches for long names
            elif avg_length > 40:
                batch_size = 20
            else:
                batch_size = 25  # Maximum batch size for very large datasets

            # Additional safety check based on total character count per batch
            estimated_batch_size = min(50, max(10, 10000 // max(1, int(avg_length))))
            return min(batch_size, estimated_batch_size)

    def _process_single_batch(self, batch_products: List[str], batch_num: int, total_batches: int, extraction_passes: int = 1, max_retries: int = 2) -> bool:
        """
        Process a single batch of products independently with retry logic.

        Args:
            batch_products: List of product names for this batch
            batch_num: Current batch number (1-based)
            total_batches: Total number of batches
            extraction_passes: Number of extraction passes
            max_retries: Maximum number of retry attempts

        Returns:
            True if batch processed successfully, False otherwise
        """
        batch_start_time = time.time()

        # Validate batch products first
        valid_batch_products = [name.strip() for name in batch_products if name and name.strip() and len(name.strip()) >= 2]

        if not valid_batch_products:
            error_msg = "No valid products after validation"
            self.batch_manager.add_batch_failure(batch_num, error_msg)
            return False

        if len(valid_batch_products) != len(batch_products):
            logger.warning(f"Batch {batch_num}: Filtered {len(batch_products) - len(valid_batch_products)} invalid products")

        # Retry logic for batch processing
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    # Wait before retry (exponential backoff)
                    wait_time = min(30, 2 ** attempt)
                    logger.info(f"Batch {batch_num}: Retry attempt {attempt} after {wait_time}s wait")
                    time.sleep(wait_time)

                logger.debug(f"Batch {batch_num}: Starting LangExtract processing for {len(valid_batch_products)} products (attempt {attempt + 1})")

                batch_result = lx.extract(
                    text_or_documents="\n".join(valid_batch_products),
                    prompt_description=self.prompt_description,
                    examples=self.examples,
                    model_id=self.model_id,
                    extraction_passes=extraction_passes,
                    fence_output=True,
                    use_schema_constraints=True, 
                )

                processing_time = time.time() - batch_start_time

                # Store successful result
                self.batch_manager.add_batch_result(batch_num, batch_result.extractions, processing_time)

                if attempt > 0:
                    logger.info(f"Batch {batch_num}: Succeeded on retry attempt {attempt}")

                return True

            except Exception as e:
                error_msg = str(e)

                # Check if this is a retryable error
                retryable_errors = ['503', '502', '429', 'timeout', 'overloaded', 'rate limit']
                is_retryable = any(err in error_msg.lower() for err in retryable_errors)

                if attempt < max_retries and is_retryable:
                    logger.warning(f"Batch {batch_num}: Retryable error on attempt {attempt + 1}: {error_msg}")
                    continue
                else:
                    processing_time = time.time() - batch_start_time
                    final_error_msg = f"Processing failed after {processing_time:.1f}s and {attempt + 1} attempts - {error_msg}"
                    self.batch_manager.add_batch_failure(batch_num, final_error_msg)
                    return False

        return False

    def extract_variants(self, product_names: List[str], extraction_passes: int = 1, batch_size_override: int = 0) -> List[Dict[str, Any]]:
        """
        Extract product variants from a list of product names with true sequential batch processing.

        Args:
            product_names: List of product names to process
            extraction_passes: Number of extraction passes to perform
            batch_size_override: Override for batch size (0 = auto-calculate)

        Returns:
            Tuple of (List of dictionaries containing extraction results, max_options)
        """
        try:
            if not product_names:
                logger.warning("No product names provided for extraction")
                return [], 0

            logger.info(f"Starting extraction for {len(product_names)} products using model {self.model_id}")

            # Pre-validate all product names
            valid_products = []
            for i, name in enumerate(product_names):
                if not name or not name.strip():
                    logger.debug(f"Skipping empty product name at index {i}")
                    continue
                if len(name.strip()) < 2:
                    logger.debug(f"Skipping too short product name at index {i}: '{name}'")
                    continue
                valid_products.append(name.strip())

            if not valid_products:
                logger.error("No valid product names found after validation")
                return [], 0

            logger.info(f"Processing {len(valid_products)} valid products (filtered from {len(product_names)} original)")

            # Determine batch size
            batch_size = self._calculate_optimal_batch_size(valid_products, batch_size_override)
            logger.info(f"Using batch size: {batch_size} for {len(valid_products)} products")
            logger.info(f"Average product name length: {sum(len(name) for name in valid_products) / len(valid_products):.1f} chars")

            # Initialize batch processing
            total_batches = (len(valid_products) + batch_size - 1) // batch_size
            self.batch_manager = BatchResultManager()  # Reset for new processing
            self.batch_manager.start_processing(total_batches)

            # Process each batch independently
            for i in range(0, len(valid_products), batch_size):
                batch = valid_products[i:i + batch_size]
                batch_num = (i // batch_size) + 1

                # Process this batch (results are stored in batch_manager)
                self._process_single_batch(batch, batch_num, total_batches, extraction_passes)

            # Get processing summary
            summary = self.batch_manager.get_processing_summary()
            logger.info(f"Batch processing summary:")
            logger.info(f"  Total batches: {summary['total_batches']}")
            logger.info(f"  Successful: {summary['successful_batches']}")
            logger.info(f"  Failed: {summary['failed_batches']}")
            logger.info(f"  Success rate: {summary['success_rate']:.1f}%")
            logger.info(f"  Total extractions: {summary['total_extractions']}")
            logger.info(f"  Total time: {summary['total_processing_time']:.1f}s")
            logger.info(f"  Average batch time: {summary['average_batch_time']:.1f}s")

            if summary['failed_batches'] > 0:
                logger.warning(f"Failed batch numbers: {summary['failed_batch_numbers']}")

            # Get all successful extractions
            all_extractions = self.batch_manager.get_all_extractions()

            if not all_extractions:
                logger.error("No successful extractions from any batch")
                return [], 0

            # Create result object for HTML visualization
            class MockResult:
                def __init__(self, extractions):
                    self.extractions = extractions
            self.last_extraction_result = MockResult(all_extractions)

            # Post-process the extractions into structured data
            rows = []
            max_options = 0

            logger.info(f"Post-processing {len(all_extractions)} total extractions")

            for ext in all_extractions:
                attrs = ext.attributes or {}
                row = {
                    "BaseProductName": attrs.get("base_product_name", "")
                }

                # Gather option pairs
                i = 1
                while True:
                    name_key = f"option{i}_name"
                    value_key = f"option{i}_value"
                    if name_key in attrs and value_key in attrs:
                        row[f"Option{i}Name"] = attrs[name_key]
                        row[f"Option{i}Value"] = attrs[value_key]
                        i += 1
                    else:
                        break

                max_options = max(max_options, i - 1)
                rows.append(row)

            logger.info(f"Successfully extracted variants for {len(rows)} products with max {max_options} options")
            return rows, max_options
            
        except Exception as e:
            logger.error(f"Error during extraction: {str(e)}")
            raise
    
    def save_results_to_csv(self, results: List[Dict[str, Any]], max_options: int, output_file: str):
        """
        Save extraction results to a CSV file with comprehensive validation.

        Args:
            results: List of extraction results
            max_options: Maximum number of options found
            output_file: Path to the output CSV file
        """
        try:
            if not results:
                logger.warning("No results to save to CSV")
                # Create empty CSV with headers
                header = ["OriginalProductName", "BaseProductName"]
                with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(header)
                return

            logger.info(f"Saving {len(results)} results to CSV with max {max_options} options")

            # Prepare CSV header
            header = ["OriginalProductName", "BaseProductName"]
            for i in range(1, max_options + 1):
                header += [f"Option{i}Name", f"Option{i}Value"]

            # Validate and clean results before writing
            valid_results = []
            for i, row in enumerate(results):
                if not isinstance(row, dict):
                    logger.warning(f"Skipping invalid result at index {i}: not a dictionary")
                    continue

                # Ensure required fields exist
                if 'OriginalProductName' not in row or 'BaseProductName' not in row:
                    logger.warning(f"Skipping result at index {i}: missing required fields")
                    continue

                valid_results.append(row)

            logger.info(f"Writing {len(valid_results)} valid results (filtered from {len(results)} total)")

            # Write to CSV
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(header)

                for row in valid_results:
                    # Ensure all values are strings and handle None values
                    csv_row = []
                    for col in header:
                        value = row.get(col, "")
                        if value is None:
                            value = ""
                        csv_row.append(str(value))
                    writer.writerow(csv_row)

            logger.info(f"Successfully saved {len(valid_results)} results to {output_file}")

            # Verify file was written correctly
            try:
                with open(output_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.reader(csvfile)
                    row_count = sum(1 for row in reader) - 1  # Subtract header row
                    logger.info(f"Verification: CSV file contains {row_count} data rows")
            except Exception as verify_error:
                logger.warning(f"Could not verify CSV file: {verify_error}")

        except Exception as e:
            logger.error(f"Error saving results to CSV: {str(e)}")
            raise

    def generate_html_visualization(self, output_dir: str = ".", html_filename: str = "visualization.html",
                                  jsonl_filename: str = "extraction_results.jsonl") -> Optional[str]:
        """
        Generate HTML visualization from the last extraction result.

        Args:
            output_dir: Directory to save the output files
            html_filename: Name of the HTML output file
            jsonl_filename: Name of the intermediate JSONL file

        Returns:
            Path to the generated HTML file, or None if visualization is disabled or no results available
        """
        if not self.enable_html_output:
            logger.info("HTML visualization is disabled")
            return None

        if not self.last_extraction_result:
            logger.warning("No extraction results available for visualization")
            return None

        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            jsonl_path = output_path / jsonl_filename
            html_path = output_path / html_filename

            logger.info(f"Generating HTML visualization...")

            # Save annotated documents to JSONL file
            lx.io.save_annotated_documents([self.last_extraction_result],
                                         output_name=jsonl_filename,
                                         output_dir=str(output_path))

            # Generate HTML visualization from the JSONL file
            html_content = lx.visualize(str(jsonl_path))

            # Write HTML content to file
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(html_content)

            logger.info(f"HTML visualization saved to {html_path}")
            return str(html_path)

        except Exception as e:
            logger.error(f"Error generating HTML visualization: {str(e)}")
            raise

    def cleanup_intermediate_files(self, output_dir: str = ".", jsonl_filename: str = "extraction_results.jsonl"):
        """
        Clean up intermediate JSONL files if desired.

        Args:
            output_dir: Directory containing the files
            jsonl_filename: Name of the JSONL file to remove
        """
        try:
            jsonl_path = Path(output_dir) / jsonl_filename
            if jsonl_path.exists():
                jsonl_path.unlink()
                logger.debug(f"Cleaned up intermediate file: {jsonl_path}")
        except Exception as e:
            logger.warning(f"Could not clean up intermediate file: {str(e)}")


def main():
    """Main application entry point."""
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract product variants using LangExtract")
    parser.add_argument("--input-csv", required=True, help="Path to input CSV file containing product names")
    parser.add_argument("--output-csv", default="extracted_variants.csv", help="Path to output CSV file")
    parser.add_argument("--column-name", default="product_name", help="Name of the column containing product names")
    parser.add_argument("--model-id", default="gemini-2.5-flash", help="Model ID to use for extraction")
    parser.add_argument("--extraction-passes", type=int, default=1, help="Number of extraction passes")
    parser.add_argument("--batch-size", type=int, default=0, help="Batch size for processing (0 = auto-calculate)")
    parser.add_argument("--max-length", type=int, default=500, help="Maximum product name length (characters)")
    parser.add_argument("--min-length", type=int, default=2, help="Minimum product name length (characters)")

    # HTML visualization options
    parser.add_argument("--enable-html", action="store_true", help="Enable HTML visualization output")
    parser.add_argument("--html-output", default="visualization.html", help="Path to HTML visualization output file")
    parser.add_argument("--output-dir", default=".", help="Directory for all output files (CSV and HTML)")
    parser.add_argument("--keep-jsonl", action="store_true", help="Keep intermediate JSONL file (for debugging)")
    
    args = parser.parse_args()
    
    # Get API key from environment
    api_key = os.getenv("LANGEXTRACT_API_KEY")
    if not api_key:
        logger.error("LANGEXTRACT_API_KEY environment variable is required")
        sys.exit(1)
    
    try:
        # Ensure output directory exists
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Adjust output paths to use the specified output directory
        csv_output_path = output_dir / Path(args.output_csv).name
        html_output_path = output_dir / Path(args.html_output).name

        # Initialize the extractor with HTML visualization option
        extractor = ProductExtractor(
            api_key=api_key,
            model_id=args.model_id,
            enable_html_output=args.enable_html
        )

        # Read product names from CSV
        product_names = extractor.read_product_names_from_csv(args.input_csv, args.column_name)

        if not product_names:
            logger.warning("No product names found in the input file")
            return

        # Extract variants
        results, max_options = extractor.extract_variants(
            product_names,
            args.extraction_passes,
            args.batch_size
        )

        # Save CSV results
        extractor.save_results_to_csv(results, max_options, str(csv_output_path))

        # Generate HTML visualization if enabled
        if args.enable_html:
            html_path = extractor.generate_html_visualization(
                output_dir=str(output_dir),
                html_filename=html_output_path.name
            )
            if html_path:
                logger.info(f"HTML visualization available at: {html_path}")

            # Clean up intermediate JSONL file unless requested to keep it
            if not args.keep_jsonl:
                extractor.cleanup_intermediate_files(output_dir=str(output_dir))

        logger.info("Processing completed successfully!")
        logger.info(f"CSV output: {csv_output_path}")
        if args.enable_html:
            logger.info(f"HTML output: {html_output_path}")

    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
