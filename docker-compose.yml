
version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: infra/Dockerfile.api
    container_name: langextract-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    env_file:
      - .env
    networks:
      - langextract-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  worker:
    build:
      context: .
      dockerfile: infra/Dockerfile.worker
    container_name: langextract-worker
    restart: unless-stopped
    env_file:
      - .env
    networks:
      - langextract-network
    deploy:
      replicas: 1

networks:
  langextract-network:
    driver: bridge
