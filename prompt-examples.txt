Multi‑Merchant Variant Extraction — Prompts (LangExtract)
1) Apparel — ONLY Color & Size
import textwrapprompt_description = textwrap.dedent("""\You are extracting variants strictly for an Apparel catalog.OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):- base_product_name : string (original product name with Color/Size removed)- option1_name      : literal "Color"- option1_value     : string color (e.g., "Navy", "Aqua", "Black") or ""- option2_name      : literal "Size"- option2_value     : string size (e.g., "S", "M", "L", "XL", "40", "41", "43") or ""STRICT ATTRIBUTE WHITELIST:Only emit these five attributes: base_product_name, option1_name, option1_value, option2_name, option2_value. Do NOT emit anything else.HARD CONSTRAINTS:1) Extract ONLY Color and Size; nothing else.2) Use EXACT substrings (no paraphrasing/normalization).3) Colors can be single/multi‑word and may be hyphen‑attached (e.g., "POPLIN-NAVY" → Color="NAVY").4) Sizes include letters (XXS, XS, S, M, L, XL, XXL, XXXL) and numerics (e.g., 40, 41, 43, 45). Keep exact token.5) Keep brand/fabric/style terms in base name (e.g., "WOOL/COTTON", "CTN", "REGOLARE", "S/S", "DENIM", "KNIT", "POPLIN", "CHINO").6) Never output Option3 or extra keys.OUTPUT RULES:- option1_name must be "Color"; option2_name must be "Size".- If only Size is present, leave Color="" (and vice‑versa).- base_product_name preserves original wording minus extracted tokens.- If ambiguous, leave the value blank.""")

2) Food & Meat — ONLY Flavor & Weight
import textwrapprompt_description = textwrap.dedent("""\You are extracting variants strictly for a Food & Meat catalog.OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):- base_product_name : string (original name with Flavor/Weight removed; drop trailing packaging descriptors like "- Cryovac Bag")- option1_name      : literal "Flavor"- option1_value     : flavor (e.g., "Kangaroo", "Chicken & Duck", "Ocean Fish") or ""- option2_name      : literal "Weight"- option2_value     : weight (e.g., "500g", "1kg", "2.5kg", "10kg", "1 lb", "16oz") or ""STRICT ATTRIBUTE WHITELIST:Only emit these five attributes: base_product_name, option1_name, option1_value, option2_name, option2_value. Nothing else.HARD CONSTRAINTS:1) Extract ONLY Flavor and Weight.2) Use EXACT substrings; no unit conversions.3) Treat multi‑word and ampersand flavors as one token (e.g., "Chicken & Beef").4) Keep cut/process words (mince, chopped, cheek, tongue, fore 1/4, rib, diced, ground, mix) inside base name unless they clearly form a standalone flavor.5) Strip common packaging descriptors from the end (e.g., "- Cryovac Bag", "Pack", "Bag", "Pouch", "Tray", "Vacuum Pack").6) Never output Option3 or extra keys.OUTPUT RULES:- option1_name="Flavor"; option2_name="Weight".- If only Weight appears, set Flavor=""; if only Flavor appears, set Weight="".- If ambiguous, leave blank.""")

3) CBD — ONLY Size, Strength & Flavor
import textwrapprompt_description = textwrap.dedent("""\You are extracting variants strictly for a CBD catalog.OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):- base_product_name : string (original name with Size/Strength/Flavor removed)- option1_name      : literal "Size"- option1_value     : size token (e.g., "Small", "Medium", "Large", "30ml", "60ml", "30ct") or ""- option2_name      : literal "Strength"- option2_value     : potency token (e.g., "25mg", "50mg", "100mg", "500mg", "1000mg", "25mg/mL") or ""- option3_name      : literal "Flavor"- option3_value     : flavor (e.g., "Natural", "Spearmint", "Cherry", "Strawberry", "Sweet Leaf") or ""STRICT ATTRIBUTE WHITELIST:Only emit these seven attributes: base_product_name, option1_name, option1_value, option2_name, option2_value, option3_name, option3_value. Nothing else.HARD CONSTRAINTS:1) Extract ONLY the three variants.2) Use EXACT substrings; no conversions.3) Keep brand/form descriptors in base (e.g., "Full Spectrum", "Broad Spectrum", "THC‑Free", "Gummies", "Tincture", "Capsules").4) Hyphen/paren‑attached variants are valid (e.g., "(1000mg)", "- Spearmint").5) Never output Option4 or extra keys.OUTPUT RULES:- Names must be literal: option1_name="Size", option2_name="Strength", option3_name="Flavor".- If a variant is missing, leave its value blank.- If ambiguous, leave blank.""")

4) Paint — ONLY Color
import textwrapprompt_description = textwrap.dedent("""\You are extracting variants strictly for a Paint shop catalog.OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):- base_product_name : string (original product name with Color removed)- option1_name      : literal "Color"- option1_value     : color token (e.g., "BUTTERNUT", "PINK", "White", "Cherry", "LIGHT BIRCH", "LIGHT OAK", "Maple") or ""STRICT ATTRIBUTE WHITELIST:Only emit these three attributes: base_product_name, option1_name, option1_value. Nothing else.HARD CONSTRAINTS:1) Extract ONLY Color.2) Use EXACT substrings; preserve case/spacing.3) Multi‑word colors and hyphen/paren‑attached colors are valid. If attached via separator, conceptually remove the separator from base.4) Keep brand/finish/volume/codes in base (e.g., "Premium", "Interior", "Emulsion", "Semi‑Gloss", "1L").5) Never output extra keys.OUTPUT RULES:- option1_name="Color".- If ambiguous, leave blank.""")

5) Utensils & Cutlery — ONLY Color & Size
import textwrapprompt_description = textwrap.dedent("""\You are extracting variants strictly for a Utensils & Cutlery catalog.OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):- base_product_name : string (original product name with Color/Size removed)- option1_name      : literal "Color"- option1_value     : color token (e.g., "Red", "PINK", "White", "Green") or ""- option2_name      : literal "Size"- option2_value     : size token (e.g., "Small", "Medium", "Large", "XL", '12"', '10"', '8"') or ""STRICT ATTRIBUTE WHITELIST:Only emit these five attributes: base_product_name, option1_name, option1_value, option2_name, option2_value. Nothing else.HARD CONSTRAINTS:1) Extract ONLY Color and Size.2) Use EXACT substrings; keep quotes/units.3) Colors may be hyphen/paren‑attached; remove adjacent separator from base.4) Sizes accept words and literal dimensions like '12"'.5) Keep material/form/count/finish words in base (e.g., "Stainless Steel", "Non‑Stick", "Cast Iron", "Set of 6", "Serrated").6) Never output extra keys.OUTPUT RULES:- option1_name="Color"; option2_name="Size".- If only one variant is present, leave the other blank.- If ambiguous, leave blank.""")