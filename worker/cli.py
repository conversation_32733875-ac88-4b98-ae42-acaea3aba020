"""
SQS Worker CLI for processing extraction jobs.
"""

import os
import json
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List
import boto3
from botocore.exceptions import ClientError
from tenacity import retry, stop_after_attempt, wait_exponential
import logging
from dotenv import load_dotenv

from langextract_core.storage import MongoStorage
from langextract_core.batching import CSVBatchProcessor
from langextract_core.extractor import ProductExtractor
from langextract_core.models import (
    SQSMessage, ExtractionStatus, ExtractionMetrics,
    ExtractionResultDocument, ParsedResult
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ExtractionWorker:
    """Worker for processing extraction jobs from SQS."""
    
    def __init__(self):
        """Initialize the extraction worker."""
        self.storage = MongoStorage()

        # AWS configuration from environment
        aws_region = os.getenv("AWS_REGION", "ap-south-1")

        # Initialize AWS clients with region
        self.sqs_client = boto3.client('sqs', region_name=aws_region)

        # Configuration from environment
        self.queue_url = os.getenv("AWS_SQS_QUEUE_URL")
        self.concurrency = int(os.getenv("WORKER_CONCURRENCY", "1"))
        self.batch_size = int(os.getenv("BATCH_SIZE", "500"))
        self.wait_time = int(os.getenv("SQS_WAIT_TIME_SECONDS", "20"))
        self.visibility_timeout = int(os.getenv("SQS_VISIBILITY_TIMEOUT", "300"))
        self.max_retries = int(os.getenv("MAX_RETRY_ATTEMPTS", "5"))

        # LangExtract configuration
        self.api_key = os.getenv("LANGEXTRACT_API_KEY")
        self.model_id = os.getenv("LANGEXTRACT_MODEL_ID", "gemini-2.5-flash")

        if not self.queue_url:
            raise ValueError("AWS_SQS_QUEUE_URL environment variable is required")
        if not self.api_key:
            raise ValueError("LANGEXTRACT_API_KEY environment variable is required")

        logger.info(f"Worker initialized with region={aws_region}, concurrency={self.concurrency}, batch_size={self.batch_size}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def receive_messages(self) -> List[Dict[str, Any]]:
        """Receive messages from SQS with retry logic."""
        try:
            response = self.sqs_client.receive_message(
                QueueUrl=self.queue_url,
                MaxNumberOfMessages=min(self.concurrency, 10),
                WaitTimeSeconds=self.wait_time,
                VisibilityTimeout=self.visibility_timeout
            )
            
            return response.get('Messages', [])
            
        except ClientError as e:
            logger.error(f"Error receiving SQS messages: {e}")
            raise
    
    def delete_message(self, receipt_handle: str):
        """Delete processed message from SQS."""
        try:
            self.sqs_client.delete_message(
                QueueUrl=self.queue_url,
                ReceiptHandle=receipt_handle
            )
            logger.debug("Message deleted from SQS")
            
        except ClientError as e:
            logger.error(f"Error deleting SQS message: {e}")
    
    def process_message(self, message: Dict[str, Any]) -> bool:
        """
        Process a single SQS message.
        
        Args:
            message: SQS message dictionary
            
        Returns:
            True if processing succeeded, False otherwise
        """
        receipt_handle = message['ReceiptHandle']
        
        try:
            # Parse message body
            body = json.loads(message['Body'])
            sqs_message = SQSMessage(**body)
            
            logger.info(f"Processing extraction {sqs_message.extraction_id}")
            
            # Check if extraction already processed
            extraction = self.storage.get_extraction(sqs_message.extraction_id)
            if not extraction:
                logger.error(f"Extraction not found: {sqs_message.extraction_id}")
                return False
            
            if extraction.status in [ExtractionStatus.COMPLETED, ExtractionStatus.RUNNING]:
                logger.info(f"Extraction {sqs_message.extraction_id} already {extraction.status}, skipping")
                return True
            
            # Update status to running
            self.storage.update_extraction_status(
                sqs_message.extraction_id,
                ExtractionStatus.RUNNING
            )
            
            # Process the extraction
            success = self._process_extraction(sqs_message)
            
            if success:
                logger.info(f"Successfully processed extraction {sqs_message.extraction_id}")
                return True
            else:
                logger.error(f"Failed to process extraction {sqs_message.extraction_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            # Update extraction status to failed
            try:
                body = json.loads(message['Body'])
                extraction_id = body.get('extraction_id')
                if extraction_id:
                    self.storage.update_extraction_status(
                        extraction_id,
                        ExtractionStatus.FAILED,
                        errors=[str(e)]
                    )
            except:
                pass
            return False
    
    def _process_extraction(self, sqs_message: SQSMessage) -> bool:
        """
        Process the actual extraction job.
        
        Args:
            sqs_message: Parsed SQS message
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Initialize batch processor and extractor
            batch_processor = CSVBatchProcessor(self.batch_size)
            extractor = ProductExtractor(
                api_key=self.api_key,
                model_id=sqs_message.model.id,
                passes=sqs_message.model.passes,
                prompt_description=sqs_message.prompt.text
            )
            
            # Count total rows for metrics
            total_rows = batch_processor.count_csv_rows(
                sqs_message.s3.bucket,
                sqs_message.s3.key,
                sqs_message.column_name
            )
            
            total_batches = batch_processor.calculate_batch_count(total_rows)
            
            # Update metrics
            metrics = ExtractionMetrics(
                total_rows=total_rows,
                batches_total=total_batches
            )
            self.storage.update_extraction_status(
                sqs_message.extraction_id,
                ExtractionStatus.RUNNING,
                metrics=metrics
            )
            
            logger.info(f"Processing {total_rows} rows in {total_batches} batches")
            
            # Process batches sequentially
            processed_rows = 0
            batches_done = 0
            
            for batch_rows in batch_processor.stream_csv_from_s3(
                sqs_message.s3.bucket,
                sqs_message.s3.key,
                sqs_message.column_name
            ):
                try:
                    logger.info(f"🔄 Processing batch {batches_done + 1}/{total_batches} with {len(batch_rows)} rows")

                    # Process batch with extractor
                    parsed_results = extractor.process_batch(batch_rows)

                    # Log extractor results
                    if not parsed_results:
                        logger.error(f"❌ Extractor returned no results for batch {batches_done + 1}")
                        batches_done += 1
                        continue

                    logger.info(f"✅ Extractor returned {len(parsed_results)} results for batch {batches_done + 1}")

                    # Log sample of parsed results for debugging
                    if parsed_results:
                        sample_result = parsed_results[0]
                        logger.debug(f"📝 Sample parsed result: {sample_result}")

                    # Create result documents
                    result_docs = []
                    transformation_successes = 0
                    transformation_failures = 0

                    for i, (original, parsed) in enumerate(zip(batch_rows, parsed_results)):
                        try:
                            # Log the parsed data before transformation
                            logger.debug(f"🔄 Transforming row {processed_rows + i}: '{original}' -> {parsed}")

                            # Transform parsed result to match ParsedResult model
                            parsed_result = self._transform_parsed_result(parsed)

                            logger.debug(f"🔍 Creating ExtractionResultDocument for row {processed_rows + i}")
                            logger.debug(f"  - extraction_id: {sqs_message.extraction_id}")
                            logger.debug(f"  - row_index: {processed_rows + i}")
                            logger.debug(f"  - original: '{original}'")
                            logger.debug(f"  - parsed: {parsed_result}")

                            result_doc = ExtractionResultDocument(
                                extraction_id=sqs_message.extraction_id,
                                row_index=processed_rows + i,
                                original=original,
                                parsed=parsed_result
                            )

                            # Debug: Check the document after creation
                            doc_dict = result_doc.model_dump()
                            logger.debug(f"🔍 Created document dict: {doc_dict}")
                            logger.debug(f"🔍 Document parsed field: {doc_dict.get('parsed', 'MISSING')}")

                            result_docs.append(result_doc)
                            transformation_successes += 1

                            logger.debug(f"✅ Successfully transformed row {processed_rows + i}")

                        except Exception as e:
                            transformation_failures += 1
                            logger.error(f"❌ Error creating result document for row {processed_rows + i}: {e}")
                            logger.debug(f"Failed transformation input: original='{original}', parsed={parsed}")

                            # Create a result with error information
                            error_result = ParsedResult(BaseProductName=original)
                            result_doc = ExtractionResultDocument(
                                extraction_id=sqs_message.extraction_id,
                                row_index=processed_rows + i,
                                original=original,
                                parsed=error_result,
                                errors=[str(e)]
                            )
                            result_docs.append(result_doc)

                    logger.info(f"📊 Transformation summary: {transformation_successes} successful, {transformation_failures} failed")

                    # Insert results into MongoDB
                    if not result_docs:
                        logger.warning(f"⚠️  No result documents to insert for batch {batches_done + 1}")
                    else:
                        logger.info(f"💾 Inserting {len(result_docs)} documents into MongoDB for batch {batches_done + 1}")

                        try:
                            inserted_count = self.storage.insert_extraction_results(result_docs)

                            if inserted_count == len(result_docs):
                                logger.info(f"✅ Successfully inserted all {inserted_count} documents for batch {batches_done + 1}")
                            elif inserted_count > 0:
                                logger.warning(f"⚠️  Partial insertion: {inserted_count}/{len(result_docs)} documents inserted for batch {batches_done + 1}")
                            else:
                                logger.error(f"❌ Failed to insert any documents for batch {batches_done + 1}")

                        except Exception as storage_error:
                            logger.error(f"💥 MongoDB insertion failed for batch {batches_done + 1}: {storage_error}")
                            # Continue processing other batches
                            pass
                    
                    # Update progress
                    processed_rows += len(batch_rows)
                    batches_done += 1
                    
                    # Update metrics periodically
                    if batches_done % 5 == 0 or batches_done == total_batches:
                        updated_metrics = ExtractionMetrics(
                            total_rows=total_rows,
                            processed_rows=processed_rows,
                            batches_total=total_batches,
                            batches_done=batches_done
                        )
                        self.storage.update_extraction_status(
                            sqs_message.extraction_id,
                            ExtractionStatus.RUNNING,
                            metrics=updated_metrics
                        )
                    
                    logger.info(f"Processed batch {batches_done}/{total_batches} ({processed_rows}/{total_rows} rows)")
                    
                except Exception as e:
                    # Classify the error type for better debugging
                    error_type = type(e).__name__
                    error_msg = str(e)

                    if "langextract" in error_msg.lower() or "api" in error_msg.lower():
                        logger.error(f"🌐 LangExtract API error in batch {batches_done + 1}: {error_type} - {error_msg}")
                    elif "mongo" in error_msg.lower() or "database" in error_msg.lower():
                        logger.error(f"💾 Database error in batch {batches_done + 1}: {error_type} - {error_msg}")
                    elif "validation" in error_msg.lower() or "pydantic" in error_msg.lower():
                        logger.error(f"📋 Data validation error in batch {batches_done + 1}: {error_type} - {error_msg}")
                    else:
                        logger.error(f"❓ Unknown error in batch {batches_done + 1}: {error_type} - {error_msg}")

                    # Log traceback for debugging
                    import traceback
                    logger.debug(f"Batch {batches_done + 1} error traceback: {traceback.format_exc()}")

                    # Continue with next batch rather than failing entire job
                    batches_done += 1
                    continue
            
            # Mark as completed
            final_metrics = ExtractionMetrics(
                total_rows=total_rows,
                processed_rows=processed_rows,
                batches_total=total_batches,
                batches_done=batches_done
            )
            
            final_status = ExtractionStatus.COMPLETED if batches_done == total_batches else ExtractionStatus.PARTIAL
            
            self.storage.update_extraction_status(
                sqs_message.extraction_id,
                final_status,
                metrics=final_metrics
            )
            
            logger.info(f"Extraction {sqs_message.extraction_id} completed with status {final_status}")
            return True
            
        except Exception as e:
            logger.error(f"Error in extraction processing: {e}")
            self.storage.update_extraction_status(
                sqs_message.extraction_id,
                ExtractionStatus.FAILED,
                errors=[str(e)]
            )
            return False

    def _transform_parsed_result(self, parsed_dict: Dict[str, Any]) -> ParsedResult:
        """
        Transform dynamic parsed result dictionary to ParsedResult model.

        Args:
            parsed_dict: Dictionary with dynamic option keys

        Returns:
            ParsedResult instance
        """
        try:
            logger.debug(f"🔄 _transform_parsed_result input: {parsed_dict}")

            # Start with base product name
            base_name = parsed_dict.get("BaseProductName", "")
            logger.debug(f"🔍 Extracted BaseProductName: '{base_name}'")

            result_data = {
                "BaseProductName": base_name
            }

            # Map dynamic options to fixed model fields
            option_mapping = {
                1: ("Option1Name", "Option1Value"),
                2: ("Option2Name", "Option2Value"),
                3: ("Option3Name", "Option3Value")
            }

            for i, (name_field, value_field) in option_mapping.items():
                option_name_key = f"Option{i}Name"
                option_value_key = f"Option{i}Value"

                logger.debug(f"🔍 Checking for {option_name_key} and {option_value_key}")

                if option_name_key in parsed_dict and option_value_key in parsed_dict:
                    name_val = parsed_dict[option_name_key]
                    value_val = parsed_dict[option_value_key]
                    result_data[name_field] = name_val
                    result_data[value_field] = value_val
                    logger.debug(f"✅ Added Option{i}: {name_field}='{name_val}', {value_field}='{value_val}'")
                else:
                    result_data[name_field] = None
                    result_data[value_field] = None
                    logger.debug(f"⚠️  Option{i} not found, setting to None")

            logger.debug(f"🔍 Final result_data before ParsedResult creation: {result_data}")
            parsed_result = ParsedResult(**result_data)
            logger.debug(f"✅ Created ParsedResult: {parsed_result}")

            return parsed_result

        except Exception as e:
            logger.error(f"Error transforming parsed result: {e}")
            # Return minimal valid result
            return ParsedResult(BaseProductName=parsed_dict.get("BaseProductName", ""))

    def run(self):
        """Main worker loop."""
        logger.info("Starting SQS worker...")
        
        with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
            while True:
                try:
                    # Receive messages
                    messages = self.receive_messages()
                    
                    if not messages:
                        logger.debug("No messages received, continuing...")
                        continue
                    
                    logger.info(f"Received {len(messages)} messages")
                    
                    # Process messages concurrently
                    futures = []
                    for message in messages:
                        future = executor.submit(self.process_message, message)
                        futures.append((future, message))
                    
                    # Wait for completion and delete successful messages
                    for future, message in futures:
                        try:
                            success = future.result(timeout=self.visibility_timeout - 30)
                            if success:
                                self.delete_message(message['ReceiptHandle'])
                        except Exception as e:
                            logger.error(f"Error processing message: {e}")
                
                except KeyboardInterrupt:
                    logger.info("Received interrupt signal, shutting down...")
                    break
                except Exception as e:
                    logger.error(f"Error in worker loop: {e}")
                    time.sleep(5)  # Brief pause before retrying


def main():
    """Main CLI entry point."""
    try:
        worker = ExtractionWorker()
        worker.run()
    except Exception as e:
        logger.error(f"Worker failed to start: {e}")
        exit(1)


if __name__ == "__main__":
    main()
