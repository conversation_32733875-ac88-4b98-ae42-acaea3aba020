#!/usr/bin/env python3
"""
Test script to validate the setup and dependencies.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required packages can be imported."""
    print("Testing imports...")
    
    try:
        import pandas as pd
        print("✓ pandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas import failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv imported successfully")
    except ImportError as e:
        print(f"✗ python-dotenv import failed: {e}")
        return False
    
    try:
        import langextract as lx
        print("✓ langextract imported successfully")
    except ImportError as e:
        print(f"✗ langextract import failed: {e}")
        return False
    
    return True

def test_file_structure():
    """Test that required files and directories exist."""
    print("\nTesting file structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "Dockerfile",
        "docker-compose.yml",
        ".env.example",
        "README.md"
    ]
    
    required_dirs = [
        "data/input",
        "data/output",
        "logs"
    ]
    
    all_good = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            all_good = False
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✓ {dir_path}/ directory exists")
        else:
            print(f"✗ {dir_path}/ directory missing")
            all_good = False
    
    return all_good

def test_sample_data():
    """Test that sample data can be read."""
    print("\nTesting sample data...")
    
    sample_file = "data/input/products.csv"
    if not Path(sample_file).exists():
        print(f"✗ Sample file {sample_file} not found")
        return False
    
    try:
        import pandas as pd
        df = pd.read_csv(sample_file)
        print(f"✓ Sample CSV loaded with {len(df)} rows")
        
        if "product_name" in df.columns:
            print("✓ 'product_name' column found")
            print(f"  Sample products: {df['product_name'].head(3).tolist()}")
        else:
            print(f"✗ 'product_name' column not found. Available columns: {list(df.columns)}")
            return False
            
    except Exception as e:
        print(f"✗ Error reading sample CSV: {e}")
        return False
    
    return True

def test_environment():
    """Test environment configuration."""
    print("\nTesting environment...")
    
    if Path(".env").exists():
        print("✓ .env file exists")
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("LANGEXTRACT_API_KEY")
        if api_key and api_key != "your_api_key_here":
            print("✓ LANGEXTRACT_API_KEY is configured")
        else:
            print("⚠ LANGEXTRACT_API_KEY not configured (this is expected for initial setup)")
    else:
        print("⚠ .env file not found (copy from .env.example and configure)")
    
    return True

def main():
    """Run all tests."""
    print("LangExtract Application Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_file_structure,
        test_sample_data,
        test_environment
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✓ All tests passed! Setup looks good.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env")
        print("2. Add your LANGEXTRACT_API_KEY to .env")
        print("3. Run the application with: python main.py --input-csv data/input/products.csv")
    else:
        print("✗ Some tests failed. Please check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
