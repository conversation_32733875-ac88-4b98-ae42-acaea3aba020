"""
Prompt templates registry for loading and serving category-specific prompts.
"""

import json
import re
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

from .models import PromptTemplate

logger = logging.getLogger(__name__)


class PromptsRegistry:
    """Registry for managing prompt templates."""
    
    def __init__(self, categories_file: str = "prompts/categories.json"):
        """
        Initialize the prompts registry.
        
        Args:
            categories_file: Path to the categories JSON file
        """
        self.categories_file = categories_file
        self._templates: Dict[str, PromptTemplate] = {}
        self._load_templates()
    
    def _load_templates(self):
        """Load prompt templates from JSON file."""
        try:
            categories_path = Path(self.categories_file)
            if not categories_path.exists():
                logger.warning(f"Categories file not found: {self.categories_file}")
                return
            
            with open(categories_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for template_data in data.get('templates', []):
                template = PromptTemplate(**template_data)
                self._templates[template.category] = template
            
            logger.info(f"Loaded {len(self._templates)} prompt templates")
            
        except Exception as e:
            logger.error(f"Error loading prompt templates: {e}")
            raise
    
    def list_categories(self) -> List[Dict[str, str]]:
        """
        Get list of available prompt categories.
        
        Returns:
            List of dictionaries with category and label
        """
        return [
            {"category": template.category, "label": template.label}
            for template in self._templates.values()
        ]
    
    def get_prompt_by_category(self, category: str) -> Optional[PromptTemplate]:
        """
        Get prompt template by category.
        
        Args:
            category: Category identifier
            
        Returns:
            PromptTemplate if found, None otherwise
        """
        return self._templates.get(category)
    
    def get_all_templates(self) -> List[PromptTemplate]:
        """
        Get all prompt templates.
        
        Returns:
            List of all prompt templates
        """
        return list(self._templates.values())


def parse_prompt_examples_to_json(input_file: str = "prompt-examples.txt", 
                                 output_file: str = "prompts/categories.json") -> Dict[str, Any]:
    """
    Parse prompt-examples.txt file and convert to JSON format.
    
    Args:
        input_file: Path to the prompt examples text file
        output_file: Path to output JSON file
        
    Returns:
        Dictionary containing parsed templates
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the content to extract prompt categories
        templates = []
        
        # Pattern to match numbered sections
        section_pattern = r'(\d+)\)\s+([^—]+)—([^=]+)'
        prompt_pattern = r'prompt_description = textwrap\.dedent\("""\\\s*(.*?)\s*"""\)'
        
        sections = re.split(r'\n(?=\d+\))', content)
        
        for section in sections:
            if not section.strip():
                continue
                
            # Extract section header
            header_match = re.search(section_pattern, section)
            if not header_match:
                continue
            
            section_num = header_match.group(1)
            category_name = header_match.group(2).strip()
            constraints = header_match.group(3).strip()
            
            # Extract prompt description
            prompt_match = re.search(prompt_pattern, section, re.DOTALL)
            if not prompt_match:
                continue
            
            prompt_description = prompt_match.group(1).strip()
            
            # Create category identifier
            category_id = category_name.lower().replace(' ', '_').replace('&', '').replace(',', '').strip()
            category_id = re.sub(r'[^\w]', '_', category_id)
            category_id = re.sub(r'_+', '_', category_id).strip('_')
            
            # Determine max options based on content
            max_options = 2  # Default
            if 'option3' in prompt_description.lower():
                max_options = 3
            elif 'option2' in prompt_description.lower():
                max_options = 2
            else:
                max_options = 1
            
            template = {
                "category": category_id,
                "label": f"{category_name} — {constraints}",
                "prompt_description": prompt_description,
                "schema_hint": {"max_options": max_options},
                "is_builtin": True
            }
            
            templates.append(template)
        
        # Create output structure
        output_data = {
            "version": "1.0",
            "generated_from": input_file,
            "templates": templates
        }
        
        # Ensure output directory exists
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write to JSON file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Parsed {len(templates)} prompt templates to {output_file}")
        return output_data
        
    except Exception as e:
        logger.error(f"Error parsing prompt examples: {e}")
        raise
