"""
MongoDB persistence functions for extractions, results, and prompts.
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import pymongo
from pymongo import MongoClient, IndexModel
from pymongo.errors import DuplicateKeyError, PyMongoError
import logging

from .models import (
    ExtractionDocument, ExtractionResultDocument, PromptTemplate,
    ExtractionStatus, ExtractionMetrics, ParsedResult
)

logger = logging.getLogger(__name__)


class MongoStorage:
    """MongoDB storage layer for LangExtract application."""
    
    def __init__(self, mongo_uri: str = None, db_name: str = "langextract"):
        """
        Initialize MongoDB connection.
        
        Args:
            mongo_uri: MongoDB connection URI
            db_name: Database name
        """
        self.mongo_uri = mongo_uri or os.getenv("MONGO_URI", "mongodb://localhost:27017")
        self.db_name = db_name or os.getenv("MONGO_DB", "langextract")
        
        self.client = MongoClient(self.mongo_uri)
        self.db = self.client[self.db_name]
        
        # Collection names from environment or defaults
        self.extractions_collection = os.getenv("MONGO_EXTRACT_COLLECTION", "extractions")
        self.results_collection = os.getenv("MONGO_RESULTS_COLLECTION", "extraction_results")
        self.prompts_collection = os.getenv("MONGO_PROMPTS_COLLECTION", "prompts")
        
        # Initialize collections and indexes
        self._setup_indexes()
    
    def _setup_indexes(self):
        """Set up MongoDB indexes for optimal performance."""
        try:
            # Extractions collection indexes
            extractions = self.db[self.extractions_collection]
            extractions.create_indexes([
                IndexModel([("extraction_id", 1)], unique=True),
                IndexModel([("status", 1)]),
                IndexModel([("created_at", -1)])
            ])
            
            # Results collection indexes
            results = self.db[self.results_collection]
            results.create_indexes([
                IndexModel([("extraction_id", 1), ("row_index", 1)], unique=True),
                IndexModel([("extraction_id", 1)])
            ])
            
            # Prompts collection indexes
            prompts = self.db[self.prompts_collection]
            prompts.create_indexes([
                IndexModel([("category", 1)], unique=True)
            ])
            
            logger.info("MongoDB indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error setting up MongoDB indexes: {e}")
            raise
    
    def create_extraction(self, extraction_doc: ExtractionDocument) -> bool:
        """
        Create a new extraction document.
        
        Args:
            extraction_doc: Extraction document to create
            
        Returns:
            True if created successfully, False if already exists
        """
        try:
            collection = self.db[self.extractions_collection]
            doc_dict = extraction_doc.model_dump()
            collection.insert_one(doc_dict)
            logger.info(f"Created extraction: {extraction_doc.extraction_id}")
            return True
            
        except DuplicateKeyError:
            logger.warning(f"Extraction already exists: {extraction_doc.extraction_id}")
            return False
        except Exception as e:
            logger.error(f"Error creating extraction: {e}")
            raise
    
    def get_extraction(self, extraction_id: str) -> Optional[ExtractionDocument]:
        """
        Get extraction by ID.
        
        Args:
            extraction_id: Extraction ID to retrieve
            
        Returns:
            ExtractionDocument if found, None otherwise
        """
        try:
            collection = self.db[self.extractions_collection]
            doc = collection.find_one({"extraction_id": extraction_id})
            
            if doc:
                # Remove MongoDB _id field
                doc.pop("_id", None)
                return ExtractionDocument(**doc)
            return None
            
        except Exception as e:
            logger.error(f"Error getting extraction {extraction_id}: {e}")
            raise
    
    def update_extraction_status(self, extraction_id: str, status: ExtractionStatus, 
                                metrics: Optional[ExtractionMetrics] = None,
                                errors: Optional[List[str]] = None) -> bool:
        """
        Update extraction status and metrics.
        
        Args:
            extraction_id: Extraction ID to update
            status: New status
            metrics: Updated metrics (optional)
            errors: Error messages to add (optional)
            
        Returns:
            True if updated successfully
        """
        try:
            collection = self.db[self.extractions_collection]
            
            update_doc = {
                "status": status.value,
                "updated_at": datetime.utcnow()
            }
            
            if metrics:
                update_doc["metrics"] = metrics.model_dump()
            
            if errors:
                update_doc["$push"] = {"errors": {"$each": errors}}
            
            result = collection.update_one(
                {"extraction_id": extraction_id},
                {"$set": update_doc} if not errors else {"$set": {k: v for k, v in update_doc.items() if k != "$push"}, **update_doc}
            )
            
            if result.modified_count > 0:
                logger.debug(f"Updated extraction {extraction_id} status to {status}")
                return True
            else:
                logger.warning(f"No extraction found to update: {extraction_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating extraction status: {e}")
            raise
    
    def insert_extraction_results(self, results: List[ExtractionResultDocument]) -> int:
        """
        Insert batch of extraction results with robust error handling.

        Args:
            results: List of extraction result documents

        Returns:
            Number of documents successfully inserted
        """
        try:
            if not results:
                return 0

            collection = self.db[self.results_collection]

            # Validate and convert documents
            valid_docs = []
            validation_failures = 0

            logger.debug(f"🔍 Validating {len(results)} result documents for MongoDB insertion")

            for i, result in enumerate(results):
                try:
                    # Validate the document using Pydantic
                    if not isinstance(result, ExtractionResultDocument):
                        validation_failures += 1
                        logger.warning(f"❌ Result {i} is not an ExtractionResultDocument instance: {type(result)}")
                        continue

                    # Convert to dict and validate required fields
                    doc = result.model_dump()

                    # Ensure required fields are present
                    required_fields = ['extraction_id', 'row_index', 'original', 'parsed']
                    missing_fields = []
                    for field in required_fields:
                        if field not in doc:
                            missing_fields.append(field)

                    if missing_fields:
                        validation_failures += 1
                        logger.error(f"❌ Result {i} missing required fields: {missing_fields}")
                        continue

                    # Validate parsed result structure
                    if not isinstance(doc.get('parsed'), dict):
                        validation_failures += 1
                        logger.error(f"❌ Result {i} parsed field is not a dict: {type(doc.get('parsed'))}")
                        continue

                    # Log successful validation
                    logger.debug(f"✅ Result {i} validation passed: {doc.get('original', 'Unknown')}")
                    valid_docs.append(doc)

                except Exception as e:
                    validation_failures += 1
                    logger.error(f"❌ Error validating result document {i}: {e}")
                    logger.debug(f"Failed document {i}: {result}")
                    continue

            logger.info(f"📊 Document validation: {len(valid_docs)} valid, {validation_failures} failed")

            if not valid_docs:
                logger.warning("No valid documents to insert")
                return 0

            # Insert documents with unordered bulk operation for better performance
            try:
                logger.debug(f"💾 Attempting to insert {len(valid_docs)} valid documents into MongoDB")
                result = collection.insert_many(valid_docs, ordered=False)
                inserted_count = len(result.inserted_ids)

                if inserted_count == len(valid_docs):
                    logger.info(f"✅ Successfully inserted all {inserted_count} extraction results")
                else:
                    logger.warning(f"⚠️  Inserted {inserted_count}/{len(valid_docs)} extraction results")

                return inserted_count

            except Exception as e:
                # Handle partial insertion failures
                if hasattr(e, 'details') and 'writeErrors' in e.details:
                    write_errors = e.details['writeErrors']
                    successful_inserts = len(valid_docs) - len(write_errors)
                    logger.warning(f"Partial insertion: {successful_inserts}/{len(valid_docs)} documents inserted")

                    # Log specific errors
                    for error in write_errors[:5]:  # Log first 5 errors
                        logger.error(f"MongoDB write error: {error}")

                    return successful_inserts
                else:
                    raise

        except Exception as e:
            logger.error(f"Error inserting extraction results: {e}")
            raise
    
    def get_extraction_results(self, extraction_id: str, offset: int = 0, 
                              limit: int = 100) -> List[ExtractionResultDocument]:
        """
        Get paginated extraction results.
        
        Args:
            extraction_id: Extraction ID
            offset: Number of results to skip
            limit: Maximum number of results to return
            
        Returns:
            List of extraction result documents
        """
        try:
            collection = self.db[self.results_collection]
            cursor = collection.find(
                {"extraction_id": extraction_id}
            ).sort("row_index", 1).skip(offset).limit(limit)
            
            results = []
            for doc in cursor:
                doc.pop("_id", None)
                results.append(ExtractionResultDocument(**doc))
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting extraction results: {e}")
            raise
    
    def seed_prompt_templates(self, templates: List[PromptTemplate]) -> int:
        """
        Seed prompt templates into database.
        
        Args:
            templates: List of prompt templates to insert
            
        Returns:
            Number of templates inserted
        """
        try:
            if not templates:
                return 0
                
            collection = self.db[self.prompts_collection]
            docs = [template.model_dump() for template in templates]
            
            # Use upsert to avoid duplicates
            inserted_count = 0
            for doc in docs:
                result = collection.replace_one(
                    {"category": doc["category"]},
                    doc,
                    upsert=True
                )
                if result.upserted_id or result.modified_count > 0:
                    inserted_count += 1
            
            logger.info(f"Seeded {inserted_count} prompt templates")
            return inserted_count
            
        except Exception as e:
            logger.error(f"Error seeding prompt templates: {e}")
            raise
    
    def get_prompt_categories(self) -> List[Dict[str, str]]:
        """
        Get list of available prompt categories.
        
        Returns:
            List of dictionaries with category and label
        """
        try:
            collection = self.db[self.prompts_collection]
            cursor = collection.find({}, {"category": 1, "label": 1, "_id": 0})
            
            return list(cursor)
            
        except Exception as e:
            logger.error(f"Error getting prompt categories: {e}")
            raise
    
    def get_prompt_by_category(self, category: str) -> Optional[PromptTemplate]:
        """
        Get prompt template by category.
        
        Args:
            category: Prompt category
            
        Returns:
            PromptTemplate if found, None otherwise
        """
        try:
            collection = self.db[self.prompts_collection]
            doc = collection.find_one({"category": category})
            
            if doc:
                doc.pop("_id", None)
                return PromptTemplate(**doc)
            return None
            
        except Exception as e:
            logger.error(f"Error getting prompt by category {category}: {e}")
            raise
