"""
Product variant extraction using LangExtract.
Refactored from main.py to be reusable across different components.
"""

import os
import time
import logging
from typing import List, Dict, Any, Optional
import langextract as lx

logger = logging.getLogger(__name__)


class ProductExtractor:
    """Main class for product variant extraction using LangExtract."""

    def __init__(self, api_key: str, model_id: str = "gemini-2.5-flash", passes: int = 1, prompt_description: str = ""):
        """
        Initialize the ProductExtractor.

        Args:
            api_key: API key for the LangExtract service
            model_id: Model ID to use for extraction
            passes: Number of extraction passes
            prompt_description: Custom prompt description text
        """
        self.api_key = api_key
        self.model_id = model_id
        self.passes = passes
        self.prompt_description = prompt_description or self._get_default_prompt()
        
        # Set up the API key
        os.environ["LANGEXTRACT_API_KEY"] = self.api_key
        
        # Initialize example data for training
        self.examples = self._create_example_data()

    def _get_default_prompt(self) -> str:
        """Get default CBD prompt if none provided."""
        return """\
You are extracting variants strictly for a CBD catalog.

OUTPUT SCHEMA (exactly ONE 'product' extraction per input line):
- base_product_name : string (original product name with Size/Strength/Flavor removed)
- option1_name      : literal "Size"
- option1_value     : string (e.g., "Small", "Medium", "Large", "30ml", "60ml", "30ct") or "" if absent
- option2_name      : literal "Strength"
- option2_value     : string (e.g., "25mg", "50mg", "100mg", "500mg", "1000mg", "25mg/mL") or "" if absent
- option3_name      : literal "Flavor"
- option3_value     : string (e.g., "Natural", "Spearmint", "Cherry", "Strawberry", "Sweet Leaf") or "" if absent

STRICT ATTRIBUTE WHITELIST:
Only emit these seven attributes: base_product_name, option1_name, option1_value, option2_name, option2_value, option3_name, option3_value.
Do NOT emit any other attributes or options.

HARD CONSTRAINTS:
1) Extract ONLY Size, Strength, and Flavor. No other variant/attribute is allowed.
2) If any of the three is missing/ambiguous, leave its value blank ("").
3) Use EXACT substrings from the input; do not paraphrase, normalize, or convert units.
4) Sizes: accept common descriptors (Small/Medium/Large) and literal pack/volume indicators if present as tokens (e.g., "30ml", "60ml", "30ct").
5) Strengths: accept literal potency tokens such as "25mg", "50mg", "100mg", "500mg", "1000mg", "25mg/mL" as they appear.
6) Flavors: accept single- or multi-word flavors, including hyphenated or ampersand forms (e.g., "Sweet Leaf", "Cherry", "Natural", "Spearmint", "Strawberry").
7) Keep brand/form/quality descriptors INSIDE base_product_name (e.g., "Full Spectrum", "Broad Spectrum", "THC-Free", "Isolate", "Gummies", "Tincture", "Capsules", "Pet", "Sleep", "Calm").
8) If a flavor word appears as part of a longer brand/word (e.g., "MintyFreshX"), do NOT treat it as Flavor unless it is a standalone token/phrase.
9) Hyphen/paren patterns: if a variant is hyphen/paren-attached (e.g., "Gummies - Spearmint", "(1000mg)"), still capture the exact variant text and conceptually remove it from base_product_name.
10) Never output any Option4/extra keys. Any key beyond the seven listed makes the output invalid.

OUTPUT RULES:
- option1_name must be "Size"; option2_name must be "Strength"; option3_name must be "Flavor".
- If only one or two variants are present, leave the others as "".
- base_product_name must preserve original wording/order minus the extracted Size/Strength/Flavor tokens.
- If ambiguous, leave the respective option value blank ("") instead of guessing.

Before finalizing, VALIDATE your output strictly uses ONLY the seven allowed attribute keys and contains no other attributes.
        """

    def _create_example_data(self) -> List[lx.data.ExampleData]:
        """Create example data for training the extraction model."""
        sample_data = [
            # Strength + Flavor (no explicit size)
            ("Full Spectrum CBD Gummies 25mg Cherry",
             {"base_product_name":"Full Spectrum CBD Gummies",
              "option1_name":"Size","option1_value":"",
              "option2_name":"Strength","option2_value":"25mg",
              "option3_name":"Flavor","option3_value":"Cherry"}),

            # Strength in parens, Flavor after dash
            ("CBD Oil Tincture (1000mg) - Spearmint",
             {"base_product_name":"CBD Oil Tincture",
              "option1_name":"Size","option1_value":"",
              "option2_name":"Strength","option2_value":"1000mg",
              "option3_name":"Flavor","option3_value":"Spearmint"}),

            # Size as volume, Strength as mg/mL, Flavor natural
            ("Broad Spectrum CBD Tincture 30ml 25mg/mL Natural",
             {"base_product_name":"Broad Spectrum CBD Tincture",
              "option1_name":"Size","option1_value":"30ml",
              "option2_name":"Strength","option2_value":"25mg/mL",
              "option3_name":"Flavor","option3_value":"Natural"}),

            # Size as count, Strength token only
            ("CBD Softgels 30ct 50mg",
             {"base_product_name":"CBD Softgels",
              "option1_name":"Size","option1_value":"30ct",
              "option2_name":"Strength","option2_value":"50mg",
              "option3_name":"Flavor","option3_value":""}),

            # Explicit Size word + Strength + Flavor
            ("THC-Free CBD Gummies Large 25mg Strawberry",
             {"base_product_name":"THC-Free CBD Gummies",
              "option1_name":"Size","option1_value":"Large",
              "option2_name":"Strength","option2_value":"25mg",
              "option3_name":"Flavor","option3_value":"Strawberry"}),
        ]
        
        return [
            lx.data.ExampleData(
                text=name,
                extractions=[
                    lx.data.Extraction(
                        extraction_class="product",
                        extraction_text=name,
                        attributes=attrs
                    )
                ]
            )
            for name, attrs in sample_data
        ]

    def process_batch(self, rows: List[str]) -> List[Dict[str, Any]]:
        """
        Process a batch of product names and return parsed results.

        Args:
            rows: List of product names to process

        Returns:
            List of parsed dictionaries with BaseProductName, OptionXName/Value...
        """
        if not rows:
            return []

        # Validate inputs before processing
        if not self.prompt_description or not self.prompt_description.strip():
            logger.error("Empty prompt description - cannot process batch")
            return [self._create_empty_result() for _ in rows]

        if not self.examples:
            logger.warning("No examples provided - extraction quality may be poor")

        # Clean and validate input rows
        clean_rows = []
        for i, row in enumerate(rows):
            if not row or not isinstance(row, str):
                logger.warning(f"Invalid row {i}: {type(row)} - {repr(row)}")
                clean_rows.append("Unknown Product")
            else:
                # Clean the row but preserve meaningful content
                cleaned = row.strip()
                if not cleaned:
                    logger.warning(f"Empty row {i} - using placeholder")
                    clean_rows.append("Unknown Product")
                else:
                    clean_rows.append(cleaned)

        rows = clean_rows
            
        try:
            # Join rows for batch processing
            text_input = "\n".join(rows)

            # Log input for debugging
            logger.debug(f"Processing batch of {len(rows)} rows")
            logger.debug(f"Input text length: {len(text_input)} characters")

            # Call LangExtract with enhanced error handling
            try:
                result = lx.extract(
                    text_or_documents=text_input,
                    prompt_description=self.prompt_description,
                    examples=self.examples,
                    model_id=self.model_id,
                    extraction_passes=self.passes,
                    fence_output=True,
                    use_schema_constraints=True,
                )
            except Exception as langextract_error:
                logger.error(f"LangExtract library error: {langextract_error}")

                # Check for specific parsing error types that indicate valid JSON but library parsing issues
                error_str = str(langextract_error).lower()
                is_parsing_error = (
                    "failed to parse content" in error_str or
                    "input string does not contain valid markers" in error_str or
                    "parsing" in error_str
                )

                if is_parsing_error:
                    logger.warning("🔄 LangExtract library parsing failed, attempting manual JSON recovery...")

                    # Try to recover the raw JSON response from the error or library internals
                    recovered_result = self._attempt_json_recovery(langextract_error, text_input)

                    if recovered_result:
                        logger.info(f"✅ Successfully recovered {len(recovered_result.extractions)} extractions from JSON")
                        result = recovered_result
                    else:
                        logger.error("❌ Failed to recover JSON data, returning empty results")
                        return [self._create_empty_result() for _ in rows]

                elif "api" in error_str or "request" in error_str or "auth" in error_str:
                    logger.error("🌐 LangExtract API request failed - check API key and connectivity")
                    return [self._create_empty_result() for _ in rows]
                else:
                    logger.error(f"❓ Unknown LangExtract error: {langextract_error}")
                    return [self._create_empty_result() for _ in rows]

            # Debug: Log the result structure for troubleshooting
            logger.debug(f"LangExtract result type: {type(result)}")
            if hasattr(result, '__dict__'):
                logger.debug(f"LangExtract result attributes: {list(result.__dict__.keys())}")

            # Validate LangExtract response
            if not hasattr(result, 'extractions') or result.extractions is None:
                logger.warning("LangExtract returned no extractions")
                # Try alternative response formats
                if hasattr(result, 'data') and hasattr(result.data, 'extractions'):
                    logger.info("Found extractions in result.data.extractions")
                    result.extractions = result.data.extractions
                elif isinstance(result, dict) and 'extractions' in result:
                    logger.info("Found extractions in dict format")
                    # Convert dict to object-like structure
                    class DictAsObject:
                        def __init__(self, d):
                            self.extractions = d['extractions']
                    result = DictAsObject(result)
                else:
                    logger.error("No extractions found in any expected format")
                    return [self._create_empty_result() for _ in rows]

            # Log successful API response
            logger.info(f"✅ LangExtract API call successful - received {len(result.extractions)} extractions")

            # Convert extractions to structured format
            parsed_results = []
            extractions_count = len(result.extractions)
            successful_parses = 0
            failed_parses = 0

            # Handle mismatch between input rows and extractions
            if extractions_count != len(rows):
                logger.warning(f"Extraction count ({extractions_count}) doesn't match input rows ({len(rows)})")

            for i, ext in enumerate(result.extractions):
                try:
                    parsed_result = self._parse_single_extraction(ext, i)

                    # Log successful parsing
                    if parsed_result.get("BaseProductName"):
                        successful_parses += 1
                        logger.debug(f"✅ Successfully parsed extraction {i}: {parsed_result.get('BaseProductName')}")
                    else:
                        logger.warning(f"⚠️  Extraction {i} parsed but has empty BaseProductName")

                    parsed_results.append(parsed_result)

                except Exception as e:
                    failed_parses += 1
                    logger.error(f"❌ Error parsing extraction {i}: {e}")
                    logger.debug(f"Extraction {i} data: {ext}")
                    parsed_results.append(self._create_empty_result())

            # Ensure we have results for all input rows
            while len(parsed_results) < len(rows):
                parsed_results.append(self._create_empty_result())

            # Truncate if we have too many results
            if len(parsed_results) > len(rows):
                parsed_results = parsed_results[:len(rows)]

            # Log parsing summary
            logger.info(f"📊 Parsing summary: {successful_parses} successful, {failed_parses} failed, {len(parsed_results)} total results")

            return parsed_results

        except Exception as e:
            # This should only catch unexpected errors, not normal processing issues
            logger.error(f"💥 Unexpected error in batch processing (not LangExtract API): {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Return empty results instead of raising - let the worker handle it
            logger.warning(f"🔄 Returning empty results for {len(rows)} rows due to unexpected error")
            return [self._create_empty_result() for _ in rows]

    def _parse_single_extraction(self, extraction, index: int) -> Dict[str, Any]:
        """
        Parse a single LangExtract extraction into structured format.
        Handles both direct attributes and nested product_attributes structure.

        Args:
            extraction: Single extraction from LangExtract result
            index: Index of the extraction for logging

        Returns:
            Parsed result dictionary
        """
        try:
            # Handle different response formats from LangExtract
            logger.debug(f"🔍 Processing extraction {index}: {type(extraction)}")

            attrs = self._extract_attributes(extraction, index)

            # Debug: Log the extracted attributes
            logger.debug(f"🔍 Extraction {index} extracted attributes: {attrs}")

            if not attrs:
                logger.warning(f"❌ Extraction {index}: No valid attributes found")
                logger.debug(f"Extraction {index} raw data: {extraction}")
                return self._create_empty_result()

            # Extract base product name with validation
            base_name = attrs.get("base_product_name", "")
            logger.debug(f"🔍 Extraction {index} base_product_name: '{base_name}' (type: {type(base_name)})")

            if not isinstance(base_name, str):
                logger.warning(f"⚠️  Extraction {index} base_product_name is not a string: {type(base_name)}")
                base_name = str(base_name) if base_name is not None else ""

            parsed_result = {
                "BaseProductName": base_name.strip()
            }

            logger.debug(f"🔍 Extraction {index} initial parsed_result: {parsed_result}")

            # Gather option pairs with validation
            i = 1
            max_options = 10  # Prevent infinite loops
            while i <= max_options:
                name_key = f"option{i}_name"
                value_key = f"option{i}_value"

                logger.debug(f"🔍 Extraction {index} checking for {name_key} and {value_key}")

                if name_key in attrs and value_key in attrs:
                    option_name = attrs[name_key]
                    option_value = attrs[value_key]

                    logger.debug(f"🔍 Extraction {index} found option {i}: name='{option_name}', value='{option_value}'")

                    # Validate and convert to strings
                    if option_name is not None:
                        option_name = str(option_name).strip()
                    if option_value is not None:
                        option_value = str(option_value).strip()

                    # Only add if both name and value are present
                    if option_name and option_value:
                        parsed_result[f"Option{i}Name"] = option_name
                        parsed_result[f"Option{i}Value"] = option_value
                        logger.debug(f"✅ Extraction {index} added Option{i}: {option_name} = {option_value}")
                    else:
                        logger.debug(f"⚠️  Extraction {index} skipping empty Option{i}: name='{option_name}', value='{option_value}'")

                    i += 1
                else:
                    logger.debug(f"🔍 Extraction {index} no more options found at {i}")
                    break

            logger.debug(f"✅ Extraction {index} final parsed_result: {parsed_result}")
            return parsed_result

        except Exception as e:
            logger.error(f"Error parsing single extraction {index}: {e}")
            return self._create_empty_result()

    def _extract_attributes(self, extraction, index: int) -> Dict[str, Any]:
        """
        Extract attributes from LangExtract response, handling different formats.

        Args:
            extraction: Single extraction from LangExtract result
            index: Index for logging

        Returns:
            Dictionary of attributes or empty dict if not found
        """
        try:
            logger.debug(f"🔍 _extract_attributes for extraction {index}: type={type(extraction)}")

            # First, let's inspect the extraction object thoroughly
            if hasattr(extraction, '__dict__'):
                logger.debug(f"🔍 Extraction {index} object __dict__: {extraction.__dict__}")

            # Method 1: Check for direct attributes (original expected format)
            if hasattr(extraction, 'attributes'):
                attrs = extraction.attributes
                logger.debug(f"🔍 Extraction {index} has attributes: {attrs} (type: {type(attrs)})")
                if isinstance(attrs, dict) and attrs:
                    logger.debug(f"✅ Extraction {index}: Using direct attributes format - {attrs}")
                    return attrs
                else:
                    logger.debug(f"⚠️  Extraction {index}: Has attributes but empty or not dict: {attrs}")
            else:
                logger.debug(f"🔍 Extraction {index}: No direct attributes found")

            # Method 2: Check for product_attributes nested structure (actual format)
            if hasattr(extraction, 'product_attributes'):
                attrs = extraction.product_attributes
                logger.debug(f"🔍 Extraction {index} has product_attributes: {attrs} (type: {type(attrs)})")
                if isinstance(attrs, dict) and attrs:
                    logger.debug(f"✅ Extraction {index}: Using product_attributes format - {attrs}")
                    return attrs
                else:
                    logger.debug(f"⚠️  Extraction {index}: Has product_attributes but empty or not dict: {attrs}")
            else:
                logger.debug(f"🔍 Extraction {index}: No product_attributes found")

            # Method 2.5: Check if extraction has a 'data' or similar wrapper
            for wrapper_attr in ['data', 'content', 'result', 'extraction_data']:
                if hasattr(extraction, wrapper_attr):
                    wrapper = getattr(extraction, wrapper_attr)
                    logger.debug(f"🔍 Extraction {index} has {wrapper_attr}: {wrapper} (type: {type(wrapper)})")
                    if hasattr(wrapper, 'product_attributes'):
                        attrs = wrapper.product_attributes
                        if isinstance(attrs, dict) and attrs:
                            logger.debug(f"✅ Extraction {index}: Using {wrapper_attr}.product_attributes format - {attrs}")
                            return attrs

            # Method 3: Check if extraction itself is a dict with product_attributes
            if isinstance(extraction, dict):
                logger.debug(f"🔍 Extraction {index}: Is dict with keys: {list(extraction.keys())}")

                if 'product_attributes' in extraction:
                    attrs = extraction['product_attributes']
                    if isinstance(attrs, dict) and attrs:
                        logger.debug(f"✅ Extraction {index}: Using dict product_attributes format - {attrs}")
                        return attrs
                    else:
                        logger.debug(f"⚠️  Extraction {index}: Has product_attributes key but empty or not dict: {attrs}")

                # Method 4: Check if extraction dict has attributes directly
                if 'attributes' in extraction:
                    attrs = extraction['attributes']
                    if isinstance(attrs, dict) and attrs:
                        logger.debug(f"✅ Extraction {index}: Using dict attributes format - {attrs}")
                        return attrs
                    else:
                        logger.debug(f"⚠️  Extraction {index}: Has attributes key but empty or not dict: {attrs}")

                # Method 5: Check if the extraction dict itself contains the attributes
                # (in case the structure is flattened)
                if any(key in extraction for key in ['base_product_name', 'option1_name']):
                    logger.debug(f"✅ Extraction {index}: Using flattened dict format - {extraction}")
                    return extraction
                else:
                    logger.debug(f"🔍 Extraction {index}: Dict doesn't contain expected attribute keys")
            else:
                logger.debug(f"🔍 Extraction {index}: Not a dict, type: {type(extraction)}")

            # Method 6: Check if extraction has string data that needs JSON parsing
            if hasattr(extraction, 'json') or hasattr(extraction, 'text') or hasattr(extraction, 'content'):
                for attr_name in ['json', 'text', 'content']:
                    if hasattr(extraction, attr_name):
                        json_data = getattr(extraction, attr_name)
                        logger.debug(f"🔍 Extraction {index} has {attr_name}: {json_data}")
                        if isinstance(json_data, str):
                            try:
                                import json
                                parsed_json = json.loads(json_data)
                                if isinstance(parsed_json, dict) and 'product_attributes' in parsed_json:
                                    attrs = parsed_json['product_attributes']
                                    logger.debug(f"✅ Extraction {index}: Using JSON parsed product_attributes - {attrs}")
                                    return attrs
                            except (json.JSONDecodeError, KeyError) as e:
                                logger.debug(f"🔍 Extraction {index}: Failed to parse JSON from {attr_name}: {e}")

            # Method 7: Try to access common LangExtract response patterns
            for pattern in ['result', 'output', 'response', 'extraction_result']:
                if hasattr(extraction, pattern):
                    pattern_data = getattr(extraction, pattern)
                    logger.debug(f"🔍 Extraction {index} has {pattern}: {pattern_data}")
                    if isinstance(pattern_data, dict) and 'product_attributes' in pattern_data:
                        attrs = pattern_data['product_attributes']
                        logger.debug(f"✅ Extraction {index}: Using {pattern}.product_attributes - {attrs}")
                        return attrs

            # Method 8: Last resort - try to convert object to dict and look for nested structures
            if hasattr(extraction, '__dict__'):
                obj_dict = extraction.__dict__
                logger.debug(f"🔍 Extraction {index}: Trying object __dict__ conversion: {obj_dict}")

                # Look for nested objects that might contain product_attributes
                for key, value in obj_dict.items():
                    if hasattr(value, 'product_attributes'):
                        attrs = value.product_attributes
                        if isinstance(attrs, dict) and attrs:
                            logger.debug(f"✅ Extraction {index}: Found product_attributes in {key} - {attrs}")
                            return attrs
                    elif isinstance(value, dict) and 'product_attributes' in value:
                        attrs = value['product_attributes']
                        if isinstance(attrs, dict) and attrs:
                            logger.debug(f"✅ Extraction {index}: Found product_attributes in {key} dict - {attrs}")
                            return attrs

            # Log the structure for debugging
            logger.warning(f"❌ Extraction {index}: Unknown format. Type: {type(extraction)}")
            if hasattr(extraction, '__dict__'):
                logger.debug(f"Extraction {index} object attributes: {list(extraction.__dict__.keys())}")
                logger.debug(f"Extraction {index} object values: {extraction.__dict__}")
            elif isinstance(extraction, dict):
                logger.debug(f"Extraction {index} dict keys: {list(extraction.keys())}")
                logger.debug(f"Extraction {index} dict values: {extraction}")
            else:
                logger.debug(f"Extraction {index} raw value: {extraction}")

            # Final attempt: if this is a string, try to find JSON-like patterns
            if isinstance(extraction, str):
                logger.debug(f"🔍 Extraction {index}: String format, checking for JSON patterns")
                try:
                    import json
                    import re

                    # Look for JSON-like patterns in the string
                    json_pattern = r'\{.*"product_attributes".*\}'
                    match = re.search(json_pattern, extraction, re.DOTALL)
                    if match:
                        json_str = match.group(0)
                        parsed = json.loads(json_str)
                        if 'product_attributes' in parsed:
                            attrs = parsed['product_attributes']
                            logger.debug(f"✅ Extraction {index}: Extracted from JSON string - {attrs}")
                            return attrs
                except Exception as e:
                    logger.debug(f"🔍 Extraction {index}: Failed to parse JSON from string: {e}")

            return {}

        except Exception as e:
            logger.error(f"Error extracting attributes from extraction {index}: {e}")
            return {}

    def _attempt_json_recovery(self, langextract_error, text_input: str):
        """
        Attempt to recover valid JSON data when LangExtract library parsing fails.

        Args:
            langextract_error: The exception thrown by LangExtract
            text_input: The original input text

        Returns:
            Mock result object with extractions, or None if recovery fails
        """
        try:
            logger.debug("🔍 Attempting JSON recovery from LangExtract error...")

            # Method 1: Try to extract JSON from error message or traceback
            error_str = str(langextract_error)
            json_data = self._extract_json_from_error(error_str)

            if not json_data:
                # Method 2: Try to make a direct API call to get raw response
                json_data = self._make_direct_api_call(text_input)

            if not json_data:
                # Method 3: Check if the error object contains response data
                json_data = self._extract_json_from_error_object(langextract_error)

            if json_data:
                logger.debug(f"🔍 Recovered JSON data: {json_data}")

                # Convert JSON to mock LangExtract result format
                mock_result = self._create_mock_result_from_json(json_data)
                return mock_result

            logger.debug("❌ No JSON data could be recovered")
            return None

        except Exception as e:
            logger.error(f"Error during JSON recovery: {e}")
            return None

    def _extract_json_from_error(self, error_str: str):
        """Extract JSON data from error message if present."""
        try:
            logger.debug(f"🔍 Extracting JSON from error message: {error_str[:200]}...")

            # Use the enhanced JSON extraction method
            json_data = self._extract_json_from_text(error_str)
            if json_data:
                logger.debug("✅ Successfully extracted JSON from error message")
                return json_data

            # Also check the full traceback if available
            import traceback
            tb_str = traceback.format_exc()
            if tb_str and tb_str != error_str:
                json_data = self._extract_json_from_text(tb_str)
                if json_data:
                    logger.debug("✅ Successfully extracted JSON from traceback")
                    return json_data

            # Check recent log output for "Top inference result" pattern
            json_data = self._extract_from_recent_logs()
            if json_data:
                logger.debug("✅ Successfully extracted JSON from recent logs")
                return json_data

            return None

        except Exception as e:
            logger.debug(f"Error extracting JSON from error string: {e}")
            return None

    def _extract_json_from_error_object(self, error_obj):
        """Extract JSON data from the error object itself."""
        try:
            # Check if the error object has response data
            for attr in ['response', 'data', 'content', 'json', 'text']:
                if hasattr(error_obj, attr):
                    data = getattr(error_obj, attr)
                    if isinstance(data, str):
                        try:
                            import json
                            parsed = json.loads(data)
                            if isinstance(parsed, dict) and 'extractions' in parsed:
                                logger.debug(f"✅ Found JSON in error object.{attr}")
                                return parsed
                        except json.JSONDecodeError:
                            continue
                    elif isinstance(data, dict) and 'extractions' in data:
                        logger.debug(f"✅ Found dict in error object.{attr}")
                        return data

            return None

        except Exception as e:
            logger.debug(f"Error extracting JSON from error object: {e}")
            return None

    def _make_direct_api_call(self, text_input: str):
        """
        Attempt to intercept or recover the raw API response.
        This tries various methods to get the JSON before LangExtract parsing fails.
        """
        try:
            logger.debug("🔍 Attempting to recover raw API response...")

            # Method 1: Try to access LangExtract's internal HTTP session or response cache
            json_data = self._try_langextract_internals()
            if json_data:
                return json_data

            # Method 2: Try to monkey-patch the HTTP response to capture raw data
            json_data = self._try_response_interception(text_input)
            if json_data:
                return json_data

            # Method 3: Check for cached responses or temporary files
            json_data = self._try_cached_responses()
            if json_data:
                return json_data

            # Method 4: Try to intercept logging output during API call
            json_data = self._try_logging_interception(text_input)
            if json_data:
                return json_data

            # Method 5: Try to make a fresh API call with error capture
            json_data = self._try_fresh_api_call_with_capture(text_input)
            if json_data:
                return json_data

            logger.debug("⚠️  Could not recover raw API response")
            return None

        except Exception as e:
            logger.debug(f"Error attempting direct API recovery: {e}")
            return None

    def _try_langextract_internals(self):
        """Try to access LangExtract's internal state for raw response data."""
        try:
            import langextract as lx

            # Check if LangExtract has any cached responses or internal state
            if hasattr(lx, '_last_response'):
                response = lx._last_response
                if isinstance(response, str):
                    import json
                    return json.loads(response)
                elif isinstance(response, dict):
                    return response

            # Check for other common internal attributes
            for attr in ['_response_cache', '_last_json', '_raw_response']:
                if hasattr(lx, attr):
                    data = getattr(lx, attr)
                    if data:
                        logger.debug(f"✅ Found data in lx.{attr}")
                        if isinstance(data, str):
                            import json
                            return json.loads(data)
                        elif isinstance(data, dict):
                            return data

            return None

        except Exception as e:
            logger.debug(f"Error accessing LangExtract internals: {e}")
            return None

    def _try_response_interception(self, text_input: str):
        """Try to intercept the HTTP response by monkey-patching requests."""
        try:
            import langextract as lx

            # Store the original requests methods
            original_post = None
            original_get = None
            captured_response = None

            try:
                import requests
                original_post = requests.post
                original_get = requests.get

                def capture_post(*args, **kwargs):
                    nonlocal captured_response
                    response = original_post(*args, **kwargs)
                    try:
                        if response.headers.get('content-type', '').startswith('application/json'):
                            captured_response = response.json()
                            logger.debug("✅ Captured JSON response via monkey-patch")
                    except:
                        pass
                    return response

                def capture_get(*args, **kwargs):
                    nonlocal captured_response
                    response = original_get(*args, **kwargs)
                    try:
                        if response.headers.get('content-type', '').startswith('application/json'):
                            captured_response = response.json()
                            logger.debug("✅ Captured JSON response via monkey-patch")
                    except:
                        pass
                    return response

                # Monkey-patch requests
                requests.post = capture_post
                requests.get = capture_get

                # Try the LangExtract call again
                try:
                    lx.extract(
                        text_or_documents=text_input,
                        prompt_description=self.prompt_description,
                        examples=self.examples,
                        model_id=self.model_id,
                        extraction_passes=self.passes,
                        fence_output=True,
                        use_schema_constraints=True,
                    )
                except:
                    # We expect this to fail, but we might have captured the response
                    pass

                return captured_response

            finally:
                # Restore original methods
                if original_post:
                    requests.post = original_post
                if original_get:
                    requests.get = original_get

        except Exception as e:
            logger.debug(f"Error with response interception: {e}")
            return None

    def _try_cached_responses(self):
        """Check for any cached responses or temporary files."""
        try:
            import os
            import tempfile
            import json

            # Check common cache locations
            cache_dirs = [
                tempfile.gettempdir(),
                os.path.expanduser("~/.langextract"),
                os.path.expanduser("~/.cache/langextract"),
                "/tmp/langextract"
            ]

            for cache_dir in cache_dirs:
                if os.path.exists(cache_dir):
                    for filename in os.listdir(cache_dir):
                        if 'langextract' in filename.lower() and filename.endswith('.json'):
                            filepath = os.path.join(cache_dir, filename)
                            try:
                                with open(filepath, 'r') as f:
                                    data = json.load(f)
                                    if isinstance(data, dict) and 'extractions' in data:
                                        logger.debug(f"✅ Found cached response in {filepath}")
                                        return data
                            except (json.JSONDecodeError, IOError):
                                continue

            return None

        except Exception as e:
            logger.debug(f"Error checking cached responses: {e}")
            return None

    def _try_logging_interception(self, text_input: str):
        """Try to intercept logging output during LangExtract API call."""
        try:
            import langextract as lx
            import logging
            import io

            # Create a custom log handler to capture output
            log_capture = io.StringIO()
            handler = logging.StreamHandler(log_capture)
            handler.setLevel(logging.DEBUG)

            # Add handler to capture logs
            root_logger = logging.getLogger()
            original_level = root_logger.level
            root_logger.setLevel(logging.DEBUG)
            root_logger.addHandler(handler)

            # Also try to capture absl logging (which LangExtract might use)
            try:
                from absl import logging as absl_logging
                absl_logging.set_verbosity(absl_logging.DEBUG)
            except ImportError:
                pass

            try:
                # Make the LangExtract call
                try:
                    result = lx.extract(
                        text_or_documents=text_input,
                        prompt_description=self.prompt_description,
                        examples=self.examples,
                        model_id=self.model_id,
                        extraction_passes=self.passes,
                        fence_output=True,
                        use_schema_constraints=True,
                    )
                    # If this succeeds, return it
                    return result
                except Exception:
                    # Expected to fail, but we might have captured the logs
                    pass

                # Get captured logs
                captured_logs = log_capture.getvalue()
                logger.debug(f"🔍 Captured {len(captured_logs)} chars of log output")

                if captured_logs:
                    # Look for "Top inference result" in captured logs
                    json_data = self._extract_top_inference_result(captured_logs)
                    if json_data:
                        logger.debug("✅ Found Top inference result in captured logs")
                        return json_data

                    # Also try general JSON extraction
                    json_data = self._extract_json_from_text(captured_logs)
                    if json_data:
                        logger.debug("✅ Found JSON in captured logs")
                        return json_data

                return None

            finally:
                # Clean up
                root_logger.removeHandler(handler)
                root_logger.setLevel(original_level)
                handler.close()

        except Exception as e:
            logger.debug(f"Error with logging interception: {e}")
            return None

    def _extract_from_recent_logs(self):
        """Extract JSON from recent log output, specifically looking for 'Top inference result' pattern."""
        try:
            import logging
            import io
            import sys

            # Try to capture recent log output
            # This is a bit tricky since we need to access the logging system's recent output

            # Method 1: Check if there's a way to access recent log records
            root_logger = logging.getLogger()

            # Look for handlers that might have captured recent output
            for handler in root_logger.handlers:
                if hasattr(handler, 'stream') and hasattr(handler.stream, 'getvalue'):
                    # This is a StringIO handler
                    log_content = handler.stream.getvalue()
                    json_data = self._extract_top_inference_result(log_content)
                    if json_data:
                        return json_data

            # Method 2: Try to access captured stdout/stderr if available
            if hasattr(sys.stdout, 'getvalue'):
                stdout_content = sys.stdout.getvalue()
                json_data = self._extract_top_inference_result(stdout_content)
                if json_data:
                    return json_data

            if hasattr(sys.stderr, 'getvalue'):
                stderr_content = sys.stderr.getvalue()
                json_data = self._extract_top_inference_result(stderr_content)
                if json_data:
                    return json_data

            return None

        except Exception as e:
            logger.debug(f"Error extracting from recent logs: {e}")
            return None

    def _extract_top_inference_result(self, log_content: str):
        """Extract JSON from 'Top inference result' log pattern."""
        try:
            import json
            import re

            if not log_content:
                return None

            logger.debug(f"🔍 Searching for 'Top inference result' in {len(log_content)} chars of log content")

            # Look for the specific "Top inference result" pattern with multi-line support
            # This pattern captures everything from "Top inference result:" to the closing brace
            pattern = r'Top inference result:\s*(\{.*?\n.*?\})'
            match = re.search(pattern, log_content, re.DOTALL | re.IGNORECASE)

            if match:
                raw_json_str = match.group(1)
                logger.debug(f"✅ Found 'Top inference result' pattern: {raw_json_str[:100]}...")

                # Clean the JSON string by removing Docker log prefixes
                cleaned_json = self._clean_docker_log_json(raw_json_str)

                try:
                    parsed = json.loads(cleaned_json)
                    if self._validate_json_data(parsed):
                        logger.debug("✅ Successfully parsed Top inference result JSON")
                        return parsed
                except json.JSONDecodeError as e:
                    logger.debug(f"Failed to parse cleaned JSON: {e}")
                    logger.debug(f"Cleaned JSON was: {cleaned_json[:200]}...")

            # Try a more aggressive pattern that captures the entire JSON block
            # Look for the start of the JSON and capture until we find a balanced closing brace
            json_start_pattern = r'Top inference result:\s*\{'
            start_match = re.search(json_start_pattern, log_content, re.IGNORECASE)

            if start_match:
                start_pos = start_match.start()
                # Find the JSON block by looking for balanced braces
                json_block = self._extract_balanced_json_block(log_content[start_pos:])

                if json_block:
                    # Extract just the JSON part (after "Top inference result:")
                    json_start = json_block.find('{')
                    if json_start >= 0:
                        raw_json = json_block[json_start:]
                        cleaned_json = self._clean_docker_log_json(raw_json)

                        try:
                            parsed = json.loads(cleaned_json)
                            if self._validate_json_data(parsed):
                                logger.debug("✅ Successfully parsed JSON block")
                                return parsed
                        except json.JSONDecodeError as e:
                            logger.debug(f"Failed to parse JSON block: {e}")

            return None

        except Exception as e:
            logger.debug(f"Error extracting Top inference result: {e}")
            return None

    def _clean_docker_log_json(self, raw_json: str):
        """Clean Docker log prefixes from JSON string."""
        try:
            import re

            lines = raw_json.split('\n')
            cleaned_lines = []

            for line in lines:
                # Remove Docker log prefixes like "langextract-worker  |   "
                # Pattern: anything up to and including " | " followed by optional spaces
                cleaned_line = re.sub(r'^[^|]*\|\s*', '', line)

                # Also remove any remaining timestamp or log level prefixes
                cleaned_line = re.sub(r'^\d{4}-\d{2}-\d{2}.*?\s-\s.*?\s-\s.*?\s-\s', '', cleaned_line)

                if cleaned_line.strip():  # Only add non-empty lines
                    cleaned_lines.append(cleaned_line)

            cleaned_json = '\n'.join(cleaned_lines)
            logger.debug(f"🔧 Cleaned JSON: {cleaned_json[:100]}...")

            return cleaned_json

        except Exception as e:
            logger.debug(f"Error cleaning Docker log JSON: {e}")
            return raw_json

    def _extract_balanced_json_block(self, text: str):
        """Extract a balanced JSON block from text."""
        try:
            brace_count = 0
            start_pos = -1

            for i, char in enumerate(text):
                if char == '{':
                    if start_pos == -1:
                        start_pos = i
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and start_pos >= 0:
                        # Found balanced JSON block
                        return text[start_pos:i+1]

            return None

        except Exception as e:
            logger.debug(f"Error extracting balanced JSON block: {e}")
            return None

    def _try_fresh_api_call_with_capture(self, text_input: str):
        """Make a fresh API call with comprehensive error capture."""
        try:
            import langextract as lx
            import sys
            import io

            # Capture stdout/stderr in case the response is logged there
            old_stdout = sys.stdout
            old_stderr = sys.stderr
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()

            try:
                sys.stdout = stdout_capture
                sys.stderr = stderr_capture

                # Try the API call again
                try:
                    result = lx.extract(
                        text_or_documents=text_input,
                        prompt_description=self.prompt_description,
                        examples=self.examples,
                        model_id=self.model_id,
                        extraction_passes=self.passes,
                        fence_output=True,
                        use_schema_constraints=True,
                    )
                    # If this succeeds, return the result
                    return result
                except Exception as e:
                    # Check captured output for JSON
                    stdout_content = stdout_capture.getvalue()
                    stderr_content = stderr_capture.getvalue()

                    logger.debug(f"🔍 Captured stdout: {len(stdout_content)} chars")
                    logger.debug(f"🔍 Captured stderr: {len(stderr_content)} chars")

                    # Look for "Top inference result" pattern specifically
                    for content_name, content in [("stdout", stdout_content), ("stderr", stderr_content), ("error", str(e))]:
                        if content:
                            # First try the specific "Top inference result" pattern
                            json_data = self._extract_top_inference_result(content)
                            if json_data:
                                logger.debug(f"✅ Found Top inference result in {content_name}")
                                return json_data

                            # Then try general JSON extraction
                            json_data = self._extract_json_from_text(content)
                            if json_data:
                                logger.debug(f"✅ Found JSON in captured {content_name}")
                                return json_data

                    return None

            finally:
                sys.stdout = old_stdout
                sys.stderr = old_stderr

        except Exception as e:
            logger.debug(f"Error with fresh API call capture: {e}")
            return None

    def _extract_json_from_text(self, text: str):
        """Extract JSON data from any text content."""
        try:
            import json
            import re

            if not text:
                return None

            # Enhanced JSON patterns to catch various formats
            json_patterns = [
                # Complete response with extractions
                r'\{[^{}]*"extractions"\s*:\s*\[[^\]]*\{[^{}]*"product_attributes"[^{}]*\}[^\]]*\][^{}]*\}',
                # Just the extractions array
                r'"extractions"\s*:\s*\[[^\]]*\{[^{}]*"product_attributes"[^{}]*\}[^\]]*\]',
                # Individual extraction with product_attributes
                r'\{[^{}]*"product_attributes"\s*:\s*\{[^{}]*"base_product_name"[^{}]*\}[^{}]*\}',
                # Just product_attributes object
                r'"product_attributes"\s*:\s*\{[^{}]*"base_product_name"[^{}]*\}',
                # Any JSON object containing base_product_name
                r'\{[^{}]*"base_product_name"[^{}]*\}',
                # Broader JSON patterns
                r'\{.*?"extractions".*?\}',
                r'\{.*?"product_attributes".*?\}'
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, text, re.DOTALL | re.IGNORECASE)
                for match in matches:
                    try:
                        # Clean up the match
                        cleaned_match = match.strip()

                        # If it's just a field, wrap it in an object
                        if cleaned_match.startswith('"'):
                            cleaned_match = '{' + cleaned_match + '}'

                        parsed = json.loads(cleaned_match)

                        # Validate that it contains useful data
                        if self._validate_json_data(parsed):
                            logger.debug(f"✅ Found valid JSON: {cleaned_match[:100]}...")
                            return parsed

                    except json.JSONDecodeError:
                        continue

            # Try to find JSON-like structures even if not perfect JSON
            return self._extract_loose_json_patterns(text)

        except Exception as e:
            logger.debug(f"Error extracting JSON from text: {e}")
            return None

    def _validate_json_data(self, data):
        """Validate that JSON data contains useful extraction information."""
        if not isinstance(data, dict):
            return False

        # Check for extractions array
        if 'extractions' in data:
            extractions = data['extractions']
            if isinstance(extractions, list) and len(extractions) > 0:
                return True

        # Check for product_attributes
        if 'product_attributes' in data:
            attrs = data['product_attributes']
            if isinstance(attrs, dict) and 'base_product_name' in attrs:
                return True

        # Check for direct base_product_name
        if 'base_product_name' in data:
            return True

        return False

    def _extract_loose_json_patterns(self, text: str):
        """Extract JSON-like patterns even if not perfect JSON."""
        try:
            import re

            # Look for key-value patterns that look like product attributes
            patterns = {
                'base_product_name': r'"base_product_name"\s*:\s*"([^"]*)"',
                'option1_name': r'"option1_name"\s*:\s*"([^"]*)"',
                'option1_value': r'"option1_value"\s*:\s*"([^"]*)"',
                'option2_name': r'"option2_name"\s*:\s*"([^"]*)"',
                'option2_value': r'"option2_value"\s*:\s*"([^"]*)"',
                'option3_name': r'"option3_name"\s*:\s*"([^"]*)"',
                'option3_value': r'"option3_value"\s*:\s*"([^"]*)"'
            }

            extracted_data = {}
            for key, pattern in patterns.items():
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    extracted_data[key] = match.group(1)

            if extracted_data and 'base_product_name' in extracted_data:
                logger.debug(f"✅ Extracted loose JSON patterns: {extracted_data}")
                # Wrap in the expected structure
                return {
                    'extractions': [{
                        'product_attributes': extracted_data
                    }]
                }

            return None

        except Exception as e:
            logger.debug(f"Error extracting loose JSON patterns: {e}")
            return None

    def _create_mock_result_from_json(self, json_data: Dict[str, Any]):
        """
        Create a mock LangExtract result object from recovered JSON data.

        Args:
            json_data: The recovered JSON data

        Returns:
            Mock result object that works with existing extraction logic
        """
        try:
            class MockExtraction:
                def __init__(self, extraction_data):
                    # Handle the product_attributes format
                    if 'product_attributes' in extraction_data:
                        self.product_attributes = extraction_data['product_attributes']

                    # Also store other fields
                    for key, value in extraction_data.items():
                        if not hasattr(self, key):
                            setattr(self, key, value)

            class MockResult:
                def __init__(self, extractions_data):
                    self.extractions = [MockExtraction(ext) for ext in extractions_data]

            # Extract extractions from JSON
            if 'extractions' in json_data:
                extractions_data = json_data['extractions']
            elif isinstance(json_data, list):
                extractions_data = json_data
            else:
                # Single extraction
                extractions_data = [json_data]

            mock_result = MockResult(extractions_data)
            logger.debug(f"✅ Created mock result with {len(mock_result.extractions)} extractions")

            return mock_result

        except Exception as e:
            logger.error(f"Error creating mock result from JSON: {e}")
            return None

    def _create_empty_result(self) -> Dict[str, Any]:
        """
        Create an empty result structure for failed extractions.

        Returns:
            Empty result dictionary
        """
        return {
            "BaseProductName": ""
        }
