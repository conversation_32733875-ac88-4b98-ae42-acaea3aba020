"""
Pydantic models for inputs, outputs, and data validation.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class ExtractionStatus(str, Enum):
    """Status of an extraction job."""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


class PromptInfo(BaseModel):
    """Prompt information for extraction."""
    category: Optional[str] = None
    text: str = Field(..., description="Raw prompt text from UI")


class ModelConfig(BaseModel):
    """Model configuration for extraction."""
    id: str = Field(default="gemini-2.5-flash", description="Model ID to use")
    passes: int = Field(default=1, description="Number of extraction passes")


class S3Config(BaseModel):
    """S3 configuration for file location."""
    bucket: str = Field(..., description="S3 bucket name")
    key: str = Field(..., description="S3 object key")


class ExtractionMetrics(BaseModel):
    """Metrics for tracking extraction progress."""
    total_rows: int = Field(default=0)
    processed_rows: int = Field(default=0)
    batches_total: int = Field(default=0)
    batches_done: int = Field(default=0)


class ExtractionDocument(BaseModel):
    """MongoDB document for extractions collection."""
    extraction_id: str = Field(..., description="Unique extraction identifier")
    s3: S3Config
    status: ExtractionStatus = ExtractionStatus.CREATED
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    prompt: PromptInfo
    model: ModelConfig
    column_name: str = Field(default="product_name")
    metrics: ExtractionMetrics = Field(default_factory=ExtractionMetrics)
    errors: List[str] = Field(default_factory=list)


class ParsedResult(BaseModel):
    """Parsed extraction result for a single product."""
    BaseProductName: str = ""
    Option1Name: Optional[str] = None
    Option1Value: Optional[str] = None
    Option2Name: Optional[str] = None
    Option2Value: Optional[str] = None
    Option3Name: Optional[str] = None
    Option3Value: Optional[str] = None

    @validator('BaseProductName')
    def validate_base_product_name(cls, v):
        """Ensure BaseProductName is a string."""
        if v is None:
            return ""
        return str(v).strip()

    @validator('Option1Name', 'Option2Name', 'Option3Name')
    def validate_option_names(cls, v):
        """Validate option names are strings or None."""
        if v is None:
            return None
        return str(v).strip() or None

    @validator('Option1Value', 'Option2Value', 'Option3Value')
    def validate_option_values(cls, v):
        """Validate option values are strings or None."""
        if v is None:
            return None
        return str(v).strip() or None


class ExtractionResultDocument(BaseModel):
    """MongoDB document for extraction_results collection."""
    extraction_id: str = Field(..., description="Reference to extraction")
    row_index: int = Field(..., description="Row index in original CSV", ge=0)
    original: str = Field(..., description="Original product name")
    parsed: ParsedResult
    errors: List[str] = Field(default_factory=list)

    @validator('extraction_id')
    def validate_extraction_id(cls, v):
        """Validate extraction ID is not empty."""
        if not v or not v.strip():
            raise ValueError('Extraction ID cannot be empty')
        return v.strip()

    @validator('original')
    def validate_original(cls, v):
        """Validate original product name."""
        if v is None:
            return ""
        return str(v).strip()

    @validator('errors')
    def validate_errors(cls, v):
        """Validate errors list contains only strings."""
        if v is None:
            return []
        return [str(error) for error in v if error is not None]


class PromptTemplate(BaseModel):
    """MongoDB document for prompts collection."""
    category: str = Field(..., description="Prompt category identifier")
    label: str = Field(..., description="Human-readable label")
    prompt_description: str = Field(..., description="Full prompt text")
    schema_hint: Dict[str, Any] = Field(default_factory=dict)
    is_builtin: bool = Field(default=True)


class SQSMessage(BaseModel):
    """SQS message format for extraction jobs."""
    type: str = Field(default="START_EXTRACTION")
    extraction_id: str = Field(..., description="UUID for the extraction")
    s3: S3Config
    prompt: PromptInfo
    model: ModelConfig
    column_name: str = Field(default="product_name")
