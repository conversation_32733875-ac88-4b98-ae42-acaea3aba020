"""
CSV streaming and batching utilities for processing large files.
"""

import csv
import io
import os
from typing import Iterator, List, Dict, Any
import boto3
from botocore.exceptions import ClientError
import logging

logger = logging.getLogger(__name__)


class CSVBatchProcessor:
    """Handles streaming CSV from S3 and batching for processing."""
    
    def __init__(self, batch_size: int = 500):
        """
        Initialize the batch processor.

        Args:
            batch_size: Number of rows per batch
        """
        self.batch_size = batch_size
        # Get AWS region from environment
        aws_region = os.getenv("AWS_REGION", "ap-south-1")
        self.s3_client = boto3.client('s3', region_name=aws_region)
    
    def stream_csv_from_s3(self, bucket: str, key: str, column_name: str = "product_name") -> Iterator[List[str]]:
        """
        Stream CSV from S3 and yield batches of product names.
        
        Args:
            bucket: S3 bucket name
            key: S3 object key
            column_name: Name of the column containing product names
            
        Yields:
            Batches of product names as lists of strings
        """
        try:
            logger.info(f"Starting to stream CSV from s3://{bucket}/{key}")
            
            # Get object from S3
            response = self.s3_client.get_object(Bucket=bucket, Key=key)
            body = response['Body']
            
            # Wrap in text wrapper for CSV reading
            text_stream = io.TextIOWrapper(body, encoding='utf-8')
            csv_reader = csv.DictReader(text_stream)
            
            # Validate column exists
            if column_name not in csv_reader.fieldnames:
                raise ValueError(f"Column '{column_name}' not found in CSV. Available columns: {csv_reader.fieldnames}")
            
            batch = []
            total_rows = 0
            
            for row in csv_reader:
                product_name = row.get(column_name, "").strip()
                
                # Skip empty or invalid rows
                if not product_name or len(product_name) < 2:
                    continue
                    
                batch.append(product_name)
                total_rows += 1
                
                # Yield batch when full
                if len(batch) >= self.batch_size:
                    logger.debug(f"Yielding batch of {len(batch)} rows (total processed: {total_rows})")
                    yield batch
                    batch = []
            
            # Yield remaining rows
            if batch:
                logger.debug(f"Yielding final batch of {len(batch)} rows (total processed: {total_rows})")
                yield batch
                
            logger.info(f"Completed streaming CSV. Total rows processed: {total_rows}")
            
        except ClientError as e:
            logger.error(f"AWS S3 error streaming CSV: {e}")
            raise
        except Exception as e:
            logger.error(f"Error streaming CSV from S3: {e}")
            raise
    
    def count_csv_rows(self, bucket: str, key: str, column_name: str = "product_name") -> int:
        """
        Count total valid rows in CSV file for metrics.
        
        Args:
            bucket: S3 bucket name
            key: S3 object key
            column_name: Name of the column containing product names
            
        Returns:
            Total number of valid rows
        """
        try:
            response = self.s3_client.get_object(Bucket=bucket, Key=key)
            body = response['Body']
            
            text_stream = io.TextIOWrapper(body, encoding='utf-8')
            csv_reader = csv.DictReader(text_stream)
            
            if column_name not in csv_reader.fieldnames:
                raise ValueError(f"Column '{column_name}' not found in CSV")
            
            count = 0
            for row in csv_reader:
                product_name = row.get(column_name, "").strip()
                if product_name and len(product_name) >= 2:
                    count += 1
                    
            return count
            
        except Exception as e:
            logger.error(f"Error counting CSV rows: {e}")
            raise
    
    def calculate_batch_count(self, total_rows: int) -> int:
        """
        Calculate total number of batches for given row count.
        
        Args:
            total_rows: Total number of rows
            
        Returns:
            Number of batches needed
        """
        return (total_rows + self.batch_size - 1) // self.batch_size
