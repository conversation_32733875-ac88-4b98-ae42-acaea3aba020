"""
Tests for Pydantic models.
"""

import pytest
from datetime import datetime
from langextract_core.models import (
    ExtractionDocument, ExtractionStatus, S3Config, PromptInfo, 
    ModelConfig, ExtractionMetrics, SQSMessage
)


def test_extraction_document_creation():
    """Test creating an extraction document."""
    s3_config = S3Config(bucket="test-bucket", key="test-key.csv")
    prompt_info = PromptInfo(category="cbd", text="Test prompt")
    model_config = ModelConfig(id="gemini-2.5-flash", passes=1)
    
    doc = ExtractionDocument(
        extraction_id="test-123",
        s3=s3_config,
        prompt=prompt_info,
        model=model_config,
        column_name="product_name"
    )
    
    assert doc.extraction_id == "test-123"
    assert doc.s3.bucket == "test-bucket"
    assert doc.s3.key == "test-key.csv"
    assert doc.prompt.category == "cbd"
    assert doc.prompt.text == "Test prompt"
    assert doc.model.id == "gemini-2.5-flash"
    assert doc.status == ExtractionStatus.CREATED
    assert isinstance(doc.created_at, datetime)
    assert isinstance(doc.updated_at, datetime)


def test_sqs_message_creation():
    """Test creating an SQS message."""
    s3_config = S3Config(bucket="test-bucket", key="test-key.csv")
    prompt_info = PromptInfo(text="Test prompt")
    model_config = ModelConfig()
    
    message = SQSMessage(
        extraction_id="test-123",
        s3=s3_config,
        prompt=prompt_info,
        model=model_config
    )
    
    assert message.type == "START_EXTRACTION"
    assert message.extraction_id == "test-123"
    assert message.s3.bucket == "test-bucket"
    assert message.prompt.text == "Test prompt"
    assert message.model.id == "gemini-2.5-flash"  # default
    assert message.column_name == "product_name"  # default


def test_extraction_metrics():
    """Test extraction metrics model."""
    metrics = ExtractionMetrics(
        total_rows=1000,
        processed_rows=500,
        batches_total=10,
        batches_done=5
    )
    
    assert metrics.total_rows == 1000
    assert metrics.processed_rows == 500
    assert metrics.batches_total == 10
    assert metrics.batches_done == 5


def test_model_serialization():
    """Test that models can be serialized to dict."""
    s3_config = S3Config(bucket="test", key="test.csv")
    dict_data = s3_config.model_dump()
    
    assert dict_data["bucket"] == "test"
    assert dict_data["key"] == "test.csv"
    
    # Test round-trip
    new_config = S3Config(**dict_data)
    assert new_config.bucket == s3_config.bucket
    assert new_config.key == s3_config.key
