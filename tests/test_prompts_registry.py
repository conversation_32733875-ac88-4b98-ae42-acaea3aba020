"""
Tests for prompts registry functionality.
"""

import pytest
import json
from pathlib import Path
from langextract_core.prompts_registry import PromptsRegistry


def test_prompts_registry_loads_categories():
    """Test that prompts registry loads categories correctly."""
    # Ensure categories file exists
    categories_file = "prompts/categories.json"
    assert Path(categories_file).exists(), "Categories file should exist"
    
    # Load registry
    registry = PromptsRegistry(categories_file)
    
    # Check categories are loaded
    categories = registry.list_categories()
    assert len(categories) > 0, "Should have loaded categories"
    
    # Check expected categories exist
    category_ids = [cat["category"] for cat in categories]
    expected_categories = ["apparel", "food_meat", "cbd", "paint", "utensils_cutlery"]
    
    for expected in expected_categories:
        assert expected in category_ids, f"Should have {expected} category"


def test_get_prompt_by_category():
    """Test getting prompt by category."""
    registry = PromptsRegistry("prompts/categories.json")
    
    # Test getting CBD prompt
    cbd_prompt = registry.get_prompt_by_category("cbd")
    assert cbd_prompt is not None, "Should find CBD prompt"
    assert cbd_prompt.category == "cbd"
    assert "CBD" in cbd_prompt.label
    assert "Size" in cbd_prompt.prompt_description
    assert "Strength" in cbd_prompt.prompt_description
    assert "Flavor" in cbd_prompt.prompt_description
    
    # Test non-existent category
    missing_prompt = registry.get_prompt_by_category("nonexistent")
    assert missing_prompt is None, "Should return None for missing category"


def test_categories_json_structure():
    """Test that the categories JSON has the expected structure."""
    with open("prompts/categories.json", 'r') as f:
        data = json.load(f)
    
    # Check top-level structure
    assert "version" in data
    assert "templates" in data
    assert isinstance(data["templates"], list)
    
    # Check each template has required fields
    for template in data["templates"]:
        assert "category" in template
        assert "label" in template
        assert "prompt_description" in template
        assert "schema_hint" in template
        assert "is_builtin" in template
        
        # Check schema hint has max_options
        assert "max_options" in template["schema_hint"]
        assert isinstance(template["schema_hint"]["max_options"], int)
        assert template["schema_hint"]["max_options"] >= 1
