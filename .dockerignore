# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Environment files
.env
.env.local
.env.*.local

# Logs
*.log
logs/

# Data directories (will be mounted as volumes)
data/

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md
