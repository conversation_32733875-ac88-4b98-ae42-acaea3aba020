# LangExtract Product Variant Extraction Platform

A production-ready, scalable platform for extracting product variants from CSV files using LangExtract. This platform includes a FastAPI service, SQS-based worker system, and MongoDB storage for processing large-scale product variant extraction jobs.

## Architecture

The platform consists of three main components:

1. **FastAPI Service**: REST API for managing extractions, prompts, and file uploads
2. **SQS Worker**: Background worker that processes extraction jobs from AWS SQS (sequential processing)
3. **MongoDB Storage**: External persistent storage for extractions, results, and prompt templates

## Features

### Core Functionality
- **Sequential Processing**: SQS-based job queue with single-job processing for reliability
- **Batch Processing**: Efficient streaming and batching of large CSV files from S3
- **Multiple Prompt Categories**: Pre-configured prompts for Apparel, Food & Meat, CBD, Paint, and Utensils
- **Custom Prompts**: Support for custom prompt text from UI
- **External MongoDB**: Structured storage of extractions, results, and metadata (not containerized)

### API Features
- **RESTful API**: FastAPI with automatic OpenAPI/Swagger documentation
- **S3 Integration**: Presigned URL generation for direct file uploads
- **Prompt Management**: API endpoints for listing and retrieving prompt templates
- **Extraction Orchestration**: Create and monitor extraction jobs
- **Paginated Results**: Efficient retrieval of extraction results

### Infrastructure
- **Docker Support**: API and Worker containerization (MongoDB external)
- **Production Ready**: Comprehensive logging, error handling, and monitoring
- **AWS Integration**: S3 for file storage, SQS for job queuing
- **Environment Configuration**: Secure configuration management

## Quick Start

### Prerequisites

- Docker and Docker Compose
- AWS Account with S3 and SQS access
- LangExtract API key
- MongoDB (external - locally installed or managed service)

### 1. Clone and Setup

```bash
git clone <your-repo>
cd langextract
```

### 2. Environment Configuration

```bash
# Copy the environment template
cp .env.example .env

# Edit .env and configure your settings:
# - LANGEXTRACT_API_KEY: Your LangExtract API key
# - AWS_REGION: Your AWS region (e.g., ap-south-1)
# - AWS_S3_BUCKET: Your S3 bucket name
# - AWS_SQS_QUEUE_URL: Your SQS queue URL
# - MONGO_URI: Your MongoDB connection string (e.g., mongodb://localhost:27017)
# - AWS credentials (via environment or IAM roles)
```

### 3. Test AWS Configuration (Optional)

```bash
# Test your AWS configuration before starting the platform
python3 scripts/test_aws_config.py
```

### 3. Initialize Prompt Templates

```bash
# Parse and create prompt templates from examples
python3 scripts/parse_prompts_simple.py
```

### 4. Start MongoDB (External)

```bash
# Install and start MongoDB locally, or use a managed service
# For local installation on macOS:
brew install mongodb-community
brew services start mongodb-community

# For Ubuntu/Debian:
sudo systemctl start mongod
```

### 5. Start the Platform

```bash
docker-compose up --build
```

This will start:
- **FastAPI service** on port 8080
- **SQS Worker** (background process, sequential processing)

Note: MongoDB is expected to be running externally (not containerized).

## API Usage

### 1. Access the API Documentation

Once the platform is running, access the interactive API documentation:

- **Swagger UI**: http://localhost:8080/docs
- **ReDoc**: http://localhost:8080/redoc

### 2. Upload a CSV File

```bash
# Get presigned URL for upload
curl -X POST "http://localhost:8080/uploads/presign" \
  -H "Content-Type: application/json" \
  -d '{"file_name": "products.csv", "content_type": "text/csv"}'

# Upload your CSV file using the returned presigned URL
curl -X PUT "<presigned_url>" \
  -H "Content-Type: text/csv" \
  --data-binary @your_products.csv
```

### 3. Start an Extraction Job

```bash
curl -X POST "http://localhost:8080/extractions/start" \
  -H "Content-Type: application/json" \
  -d '{
    "s3_key": "uploads/uuid-products.csv",
    "prompt": {
      "category": "cbd",
      "text": "Extract CBD product variants..."
    },
    "model": {
      "id": "gemini-2.5-flash",
      "passes": 1
    },
    "column_name": "product_name"
  }'
```

### 4. Monitor Extraction Progress

```bash
# Check extraction status
curl "http://localhost:8080/extractions/{extraction_id}"

# Get extraction results (paginated)
curl "http://localhost:8080/extractions/{extraction_id}/results?offset=0&limit=100"
```

### 5. Available Prompt Categories

```bash
# List all available prompt categories
curl "http://localhost:8080/prompts/categories"

# Get specific prompt template
curl "http://localhost:8080/prompts/cbd"
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input-csv` | Path to input CSV file | Required |
| `--output-csv` | Path to output CSV file | `extracted_variants.csv` |
| `--column-name` | Column name containing product names | `product_name` |
| `--model-id` | LangExtract model to use | `gemini-2.5-flash` |
| `--extraction-passes` | Number of extraction passes | `1` |
| `--enable-html` | Enable HTML visualization output | `False` |
| `--html-output` | Path to HTML visualization file | `visualization.html` |
| `--output-dir` | Directory for all output files | `.` |
| `--keep-jsonl` | Keep intermediate JSONL file | `False` |

## Output Formats

The application generates two types of output:

### CSV Output

A structured CSV file with the following columns:

| Column | Description |
|--------|-------------|
| `OriginalProductName` | Original product name from input |
| `BaseProductName` | Extracted base product name |
| `Option1Name` | Name of first option (e.g., "Color") |
| `Option1Value` | Value of first option (e.g., "Blue") |
| `Option2Name` | Name of second option (e.g., "Size") |
| `Option2Value` | Value of second option (e.g., "M") |
| ... | Additional option pairs as needed |

Example CSV output:
```csv
OriginalProductName,BaseProductName,Option1Name,Option1Value,Option2Name,Option2Value
Classic Tee Blue M,Classic Tee,Color,Blue,Size,M
Sneaker Airmax White 10 Men,Sneaker Airmax,Color,White,Size,10,Gender,Men
```

### HTML Visualization (Optional)

When `--enable-html` is used, the application also generates an interactive HTML file that provides:

- **Visual Highlighting**: Product names with color-coded extraction highlights
- **Interactive Tooltips**: Hover over highlighted text to see extracted attributes
- **Playback Controls**: Step through the extraction process with animation controls
- **Detailed View**: Complete breakdown of all extracted entities and their attributes

The HTML visualization is perfect for:
- **Quality Review**: Visually inspect extraction accuracy
- **Presentation**: Share results with stakeholders in an accessible format
- **Debugging**: Understand how the model interpreted each product name
- **Training**: Use as examples for improving extraction prompts

## HTML Visualization Workflow

The HTML visualization feature uses LangExtract's built-in visualization capabilities:

1. **Extraction**: The application processes your CSV data using LangExtract
2. **Annotation**: Results are saved to an intermediate JSONL file with detailed annotations
3. **Visualization**: LangExtract generates an interactive HTML file from the annotations
4. **Cleanup**: Intermediate files are automatically removed (unless `--keep-jsonl` is specified)

### Viewing HTML Results

After running with `--enable-html`, open the generated HTML file in any web browser:

```bash
# Open the visualization file
open data/output/visualization.html  # macOS
xdg-open data/output/visualization.html  # Linux
start data/output/visualization.html  # Windows
```

The HTML file is self-contained and can be shared or viewed offline.

## Project Structure

```
langextract/
├── langextract_core/           # Core extraction logic
│   ├── __init__.py
│   ├── extractor.py           # ProductExtractor class (refactored from main.py)
│   ├── batching.py            # CSV streaming & batching utilities
│   ├── models.py              # Pydantic models for data validation
│   ├── prompts_registry.py    # Prompt template management
│   └── storage.py             # MongoDB persistence layer
├── api/                       # FastAPI service
│   ├── __init__.py
│   ├── main.py               # FastAPI application
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── prompts.py        # Prompt management endpoints
│   │   ├── uploads.py        # S3 presigned URL endpoints
│   │   └── extractions.py    # Extraction orchestration endpoints
│   └── schemas/              # API request/response schemas
│       ├── __init__.py
│       ├── prompts.py
│       ├── uploads.py
│       └── extractions.py
├── worker/                    # SQS worker
│   ├── __init__.py
│   └── cli.py                # SQS message processor
├── infra/                     # Infrastructure configuration
│   ├── Dockerfile.api        # API service container
│   └── Dockerfile.worker     # Worker service container
├── scripts/                   # Utility scripts
│   ├── seed_prompts.py       # MongoDB prompt seeding (requires deps)
│   └── parse_prompts_simple.py # Simple prompt parsing (no deps)
├── prompts/                   # Prompt templates
│   └── categories.json       # Parsed prompt categories
├── tests/                     # Test suite
│   ├── __init__.py
│   ├── test_models.py
│   └── test_prompts_registry.py
├── docker-compose.yml         # Multi-service orchestration (API + Worker only)
├── .env.example              # Environment configuration template
├── main.py                    # Legacy CLI script (still functional)
├── requirements.txt           # Python dependencies
├── sample_products.csv       # Sample input data
├── data/                     # Local development data
│   ├── input/               # Input CSV files
│   └── output/              # Output files
└── README.md                 # This documentation
```

## Environment Variables

### Core Configuration
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `LANGEXTRACT_API_KEY` | API key for LangExtract service | Yes | - |
| `LANGEXTRACT_MODEL_ID` | Model ID to use | No | `gemini-2.5-flash` |

### MongoDB Configuration (External)
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MONGO_URI` | MongoDB connection URI | No | `mongodb://localhost:27017` |
| `MONGO_DB` | Database name | No | `langextract` |
| `MONGO_EXTRACT_COLLECTION` | Extractions collection name | No | `extractions` |
| `MONGO_RESULTS_COLLECTION` | Results collection name | No | `extraction_results` |
| `MONGO_PROMPTS_COLLECTION` | Prompts collection name | No | `prompts` |

### AWS Configuration
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `AWS_REGION` | AWS region | No | `ap-south-1` |
| `AWS_S3_BUCKET` | S3 bucket for file storage | Yes | - |
| `AWS_SQS_QUEUE_URL` | SQS queue URL for job processing | Yes | - |
| `AWS_ACCESS_KEY_ID` | AWS access key (or use IAM roles) | Yes* | - |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key (or use IAM roles) | Yes* | - |

### Worker Configuration
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `WORKER_CONCURRENCY` | Number of concurrent message processors | No | `1` |
| `BATCH_SIZE` | Rows per processing batch | No | `500` |
| `SQS_WAIT_TIME_SECONDS` | SQS long polling wait time | No | `20` |
| `SQS_VISIBILITY_TIMEOUT` | Message visibility timeout (seconds) | No | `300` |
| `MAX_RETRY_ATTEMPTS` | Maximum retry attempts for failed operations | No | `5` |

*AWS credentials can be provided via environment variables, IAM roles, or AWS credential files.

## Architecture & Data Flow

### System Components

1. **FastAPI Service** (`api/`)
   - REST API endpoints for external integration
   - Handles file upload presigning, extraction orchestration, and result retrieval
   - Validates requests and manages MongoDB interactions
   - Enqueues extraction jobs to SQS

2. **SQS Worker** (`worker/`)
   - Long-polling SQS consumer with sequential processing (WORKER_CONCURRENCY=1)
   - Streams CSV files from S3 in batches to manage memory usage
   - Processes batches through LangExtract with retry logic
   - Persists results to external MongoDB with progress tracking

3. **External MongoDB Storage** (`langextract_core/storage.py`)
   - External MongoDB instance (not containerized)
   - Stores extraction metadata, progress, and results
   - Manages prompt templates and categories
   - Provides indexed queries for efficient data retrieval

### Data Flow

```
1. Client uploads CSV → S3 (via presigned URL)
2. Client creates extraction → API validates & stores in MongoDB
3. API enqueues SQS message → Worker receives message
4. Worker streams CSV from S3 → Processes in batches
5. Worker calls LangExtract → Stores results in MongoDB
6. Client polls API → Retrieves results and status
```

### Prompt Categories

The system includes pre-configured prompt templates for:

- **Apparel**: Color and Size extraction
- **Food & Meat**: Flavor and Weight extraction
- **CBD**: Size, Strength, and Flavor extraction
- **Paint**: Color extraction
- **Utensils & Cutlery**: Color and Size extraction

Custom prompts can be provided via the API for specialized use cases.

## Troubleshooting

### AWS Configuration Issues

**Problem**: Worker fails with "You must specify a region" error
**Solution**: Ensure `AWS_REGION` is set in your `.env` file:
```bash
AWS_REGION=ap-south-1  # or your preferred region
```

**Problem**: API health check fails with "AccessDenied" for `s3:ListAllMyBuckets`
**Solution**: This is expected with restricted IAM permissions. The health check has been updated to not require broad S3 permissions. The API will still function correctly.

**Problem**: SQS or S3 operations fail with region errors
**Solution**: Ensure your `AWS_SQS_QUEUE_URL` region matches your `AWS_REGION` setting:
```bash
# If your queue is in ap-south-1, make sure:
AWS_REGION=ap-south-1
AWS_SQS_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/123456789012/extraction-queue
```

**Problem**: AWS credentials not found
**Solution**: Set your AWS credentials via environment variables:
```bash
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
```
Or use AWS credential files or IAM roles.

### Testing AWS Configuration

Run the AWS configuration test to verify your setup:
```bash
python3 scripts/test_aws_config.py
```

## Development

### Adding New Example Data

To improve extraction accuracy, you can add more training examples in the `_create_example_data()` method in `main.py`.

### Customizing Extraction Logic

The extraction prompt can be modified in the `prompt_description` attribute of the `ProductExtractor` class.

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your `LANGEXTRACT_API_KEY` is set correctly in the `.env` file
2. **CSV Column Not Found**: Verify the column name matches your CSV file structure
3. **Permission Errors**: Ensure the `data/output` directory is writable
4. **HTML Not Generated**: Check that `--enable-html` flag is used and no errors occurred during extraction
5. **HTML File Empty/Corrupted**: Ensure the extraction completed successfully before HTML generation

### Example: Complete Workflow

```bash
# 1. Prepare your data
echo "product_name" > data/input/my_products.csv
echo "Wireless Bluetooth Headphones Black Over-Ear" >> data/input/my_products.csv
echo "Gaming Laptop 16GB RAM 1TB SSD 15-inch" >> data/input/my_products.csv

# 2. Run extraction with both CSV and HTML output
docker-compose up

# 3. View results
cat data/output/extracted_variants.csv
open data/output/visualization.html
```

This will generate:
- **CSV**: Structured data ready for analysis or database import
- **HTML**: Interactive visualization for review and presentation

### Logs

Application logs are written to:
- Console output (stdout)
- `langextract_app.log` file
- `logs/` directory when using Docker

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
