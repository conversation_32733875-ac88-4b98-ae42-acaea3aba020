# MongoDB Configuration (External - not containerized)
MONGO_URI=mongodb://localhost:27017
MONGO_DB=langextract
MONGO_EXTRACT_COLLECTION=extractions
MONGO_RESULTS_COLLECTION=extraction_results
MONGO_PROMPTS_COLLECTION=prompts

# AWS Configuration
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your-bucket-name
AWS_SQS_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/123456789012/extraction-queue

# AWS Credentials (set these in your environment or use IAM roles)
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key

# LangExtract Configuration
LANGEXTRACT_API_KEY=replace-with-your-api-key
LANGEXTRACT_MODEL_ID=gemini-2.5-flash

# Worker Configuration (Sequential processing)
WORKER_CONCURRENCY=1
BATCH_SIZE=500
SQS_WAIT_TIME_SECONDS=20
SQS_VISIBILITY_TIMEOUT=300
MAX_RETRY_ATTEMPTS=5

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080
