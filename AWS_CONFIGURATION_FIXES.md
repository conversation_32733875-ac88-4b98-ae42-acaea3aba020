# AWS Configuration Fixes Summary

## 🔧 Issues Fixed

### 1. **Worker Region Error** ✅
**Problem**: SQS worker failing with "You must specify a region" error
**Root Cause**: boto3 clients were created without specifying the region_name parameter
**Solution**: Added explicit region configuration to all boto3 client instantiations

### 2. **API Health Check Permission Error** ✅
**Problem**: API health check failing due to `s3:ListAllMyBuckets` permission denial
**Root Cause**: Health check was calling `list_buckets()` which requires broad S3 permissions
**Solution**: Replaced with configuration-only checks that don't require API calls

## 📝 Changes Made

### Files Modified

1. **`worker/cli.py`**
   - Added AWS region configuration from environment variables
   - Updated SQS client creation: `boto3.client('sqs', region_name=aws_region)`
   - Enhanced logging to show configured region

2. **`langextract_core/batching.py`**
   - Added os import for environment variable access
   - Updated S3 client creation: `boto3.client('s3', region_name=aws_region)`

3. **`api/routers/uploads.py`**
   - Updated S3 client creation in `get_s3_client()` function
   - Added region configuration from environment

4. **`api/routers/extractions.py`**
   - Updated SQS client creation in `get_sqs_client()` function
   - Added region configuration from environment

5. **`api/main.py`**
   - Completely rewrote health check AWS validation
   - Removed `list_buckets()` call that required broad permissions
   - Added configuration-only checks for AWS setup
   - Enhanced health check response with detailed AWS status

### New Files Created

6. **`scripts/test_aws_config.py`**
   - New utility script to test AWS configuration
   - Validates environment variables and client creation
   - Tests credentials without requiring broad permissions
   - Provides detailed feedback on configuration issues

## 🔧 Technical Details

### AWS Region Configuration
All boto3 clients now use explicit region configuration:
```python
aws_region = os.getenv("AWS_REGION", "ap-south-1")
client = boto3.client('service', region_name=aws_region)
```

### Health Check Improvements
Old health check (problematic):
```python
boto3.client('s3').list_buckets()  # Requires s3:ListAllMyBuckets
```

New health check (permission-friendly):
```python
# Just verify client creation and configuration
boto3.client('s3', region_name=aws_region)
boto3.client('sqs', region_name=aws_region)
# Check environment variables are set
```

### Enhanced Health Check Response
```json
{
  "status": "healthy",
  "mongodb": "connected",
  "prompt_categories": 5,
  "aws": {
    "status": "configured",
    "region": "ap-south-1",
    "s3_bucket_configured": true,
    "sqs_queue_configured": true
  }
}
```

## 🚀 Benefits

1. **Works with Restricted IAM Permissions**: No longer requires broad S3 permissions
2. **Proper Region Handling**: All AWS operations use the correct region
3. **Better Error Messages**: Clear feedback when configuration is missing
4. **Validation Tools**: New script to test configuration before deployment
5. **Consistent Configuration**: All boto3 clients use the same region source

## 🧪 Testing

### Test AWS Configuration
```bash
python3 scripts/test_aws_config.py
```

This script will:
- ✅ Verify environment variables are set
- ✅ Test boto3 client creation with region
- ✅ Validate SQS queue URL region matches AWS_REGION
- ✅ Test AWS credentials without broad permission requirements

### Expected Output
```
🚀 AWS Configuration Test
========================================
🔍 Testing AWS Configuration...
AWS_REGION: ap-south-1
AWS_S3_BUCKET: your-bucket-name
AWS_SQS_QUEUE_URL: https://sqs.ap-south-1.amazonaws.com/123456789012/extraction-queue
✅ S3 client created successfully
✅ SQS client created successfully
✅ SQS queue region matches AWS_REGION
✅ AWS configuration test passed!

🔍 Testing AWS Credentials...
✅ AWS credentials found
✅ AWS credentials appear to be valid
========================================
🎉 AWS configuration looks good!
```

## 📋 Required Environment Variables

Ensure these are set in your `.env` file:
```bash
# Required
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your-bucket-name
AWS_SQS_QUEUE_URL=https://sqs.ap-south-1.amazonaws.com/123456789012/extraction-queue
LANGEXTRACT_API_KEY=your-api-key

# AWS Credentials (via environment or IAM roles)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

## 🔒 Minimum Required AWS Permissions

The platform now works with these minimal IAM permissions:

### S3 Permissions
```json
{
  "Effect": "Allow",
  "Action": [
    "s3:GetObject",
    "s3:PutObject"
  ],
  "Resource": "arn:aws:s3:::your-bucket/*"
}
```

### SQS Permissions
```json
{
  "Effect": "Allow",
  "Action": [
    "sqs:ReceiveMessage",
    "sqs:DeleteMessage",
    "sqs:SendMessage",
    "sqs:GetQueueAttributes"
  ],
  "Resource": "arn:aws:sqs:ap-south-1:123456789012:extraction-queue"
}
```

**Note**: No longer requires `s3:ListAllMyBuckets` or other broad permissions.

## ✅ Verification

After applying these fixes:
1. ✅ Worker starts without region errors
2. ✅ API health check passes with restricted permissions
3. ✅ All AWS operations use correct region configuration
4. ✅ Platform works with minimal IAM permissions
5. ✅ Clear error messages for configuration issues

The platform is now compatible with restricted AWS IAM users and properly handles region configuration across all services.
