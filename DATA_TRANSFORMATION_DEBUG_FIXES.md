# Data Transformation Debug Fixes

## 🔍 Issue Analysis

### **Problem Identified**
LangExtract API is successfully returning properly structured data with the nested `product_attributes` format, but the parsed fields are appearing as empty or null in the MongoDB `extraction_results` collection.

**Debug Evidence**:
- ✅ LangExtract API returns correct structure with `product_attributes`
- ❌ MongoDB documents have empty/null values in `parsed` fields
- ❌ Data is being lost somewhere in the transformation pipeline

### **Root Cause Investigation**
The issue appears to be in the data extraction from the LangExtract response objects. While the API returns the correct JSON structure, the actual Python objects returned by the LangExtract library may have a different attribute structure than expected.

## ✅ Comprehensive Debug Fixes Implemented

### 1. **Enhanced Extractor Debug Logging** (`langextract_core/extractor.py`)

**Added comprehensive logging at every step**:

```python
# Processing start
logger.debug(f"🔍 Processing extraction {index}: {type(extraction)}")

# Attribute extraction
logger.debug(f"🔍 Extraction {index} extracted attributes: {attrs}")

# Base product name extraction
logger.debug(f"🔍 Extraction {index} base_product_name: '{base_name}' (type: {type(base_name)})")

# Option parsing
logger.debug(f"🔍 Extraction {index} checking for {name_key} and {value_key}")
logger.debug(f"✅ Extraction {index} added Option{i}: {option_name} = {option_value}")

# Final result
logger.debug(f"✅ Extraction {index} final parsed_result: {parsed_result}")
```

**Benefits**:
- ✅ Complete visibility into extraction processing
- ✅ Identifies exactly where data is lost
- ✅ Shows the structure of LangExtract response objects
- ✅ Tracks each option extraction step

### 2. **Comprehensive Attribute Extraction Methods** (`_extract_attributes`)

**Added 8 different methods to handle various LangExtract response formats**:

```python
# Method 1: Direct attributes (original expected)
if hasattr(extraction, 'attributes') and extraction.attributes:

# Method 2: product_attributes nested structure (actual format)
if hasattr(extraction, 'product_attributes'):

# Method 3: Dict with product_attributes
if isinstance(extraction, dict) and 'product_attributes' in extraction:

# Method 4: Dict with direct attributes
if 'attributes' in extraction:

# Method 5: Flattened dict format
if any(key in extraction for key in ['base_product_name', 'option1_name']):

# Method 6: JSON string parsing
if hasattr(extraction, 'json') or hasattr(extraction, 'text'):

# Method 7: Common LangExtract patterns
for pattern in ['result', 'output', 'response', 'extraction_result']:

# Method 8: Object __dict__ conversion with nested search
if hasattr(extraction, '__dict__'):
```

**Benefits**:
- ✅ Handles any possible LangExtract response format
- ✅ Comprehensive fallback mechanisms
- ✅ Detailed logging for each method attempted
- ✅ Future-proof against API changes

### 3. **Enhanced Worker Transformation Logging** (`worker/cli.py`)

**Added detailed transformation tracking**:

```python
# Input logging
logger.debug(f"🔄 _transform_parsed_result input: {parsed_dict}")

# Base name extraction
logger.debug(f"🔍 Extracted BaseProductName: '{base_name}'")

# Option mapping
logger.debug(f"✅ Added Option{i}: {name_field}='{name_val}', {value_field}='{value_val}'")

# Final result
logger.debug(f"🔍 Final result_data before ParsedResult creation: {result_data}")
logger.debug(f"✅ Created ParsedResult: {parsed_result}")
```

**Benefits**:
- ✅ Tracks data through worker transformation
- ✅ Identifies transformation failures
- ✅ Shows input/output at each step
- ✅ Validates ParsedResult creation

### 4. **MongoDB Document Creation Debugging** (`worker/cli.py`)

**Added comprehensive document creation logging**:

```python
# Document creation details
logger.debug(f"🔍 Creating ExtractionResultDocument for row {processed_rows + i}")
logger.debug(f"  - extraction_id: {sqs_message.extraction_id}")
logger.debug(f"  - original: '{original}'")
logger.debug(f"  - parsed: {parsed_result}")

# Document validation
doc_dict = result_doc.model_dump()
logger.debug(f"🔍 Created document dict: {doc_dict}")
logger.debug(f"🔍 Document parsed field: {doc_dict.get('parsed', 'MISSING')}")
```

**Benefits**:
- ✅ Validates document structure before MongoDB insertion
- ✅ Shows exact data being stored
- ✅ Identifies Pydantic model issues
- ✅ Confirms data integrity

### 5. **Comprehensive Test Script** (`scripts/debug_data_transformation.py`)

**Created test script with exact debug data**:

```python
# Uses exact data from debug logs
extraction_data = {
    "product": "502 FS CBD Oil Pink Lemonade 250mg 1oz",
    "product_attributes": {
        "base_product_name": "502 FS CBD Oil",
        "option1_name": "Size",
        "option1_value": "1oz",
        "option2_name": "Strength",
        "option2_value": "250mg",
        "option3_name": "Flavor",
        "option3_value": "Pink Lemonade"
    }
}
```

**Test Results**:
- ✅ Manual parsing logic works correctly
- ✅ Transformation logic works correctly
- ✅ Expected MongoDB structure is correct

## 🔍 Debugging Strategy

### **Step-by-Step Data Flow Tracking**

1. **LangExtract Response**: Log the raw response structure
2. **Attribute Extraction**: Log which method successfully extracts attributes
3. **Parsing**: Log each option extraction and final parsed result
4. **Transformation**: Log input/output of worker transformation
5. **Document Creation**: Log MongoDB document structure
6. **Storage**: Log insertion success/failure

### **Debug Log Patterns to Look For**

**Successful Flow**:
```
✅ Extraction 0: Using product_attributes format - {attributes}
✅ Extraction 0 final parsed_result: {BaseProductName: "...", Option1Name: "..."}
✅ Created ParsedResult: BaseProductName='...' Option1Name='...'
✅ Created document dict: {parsed: {BaseProductName: "...", Option1Name: "..."}}
```

**Failed Flow Indicators**:
```
❌ Extraction 0: No valid attributes found
⚠️  Extraction 0: Has product_attributes but empty or not dict
❌ Extraction 0: Unknown format. Type: <class '...'>
```

## 🚀 Next Steps for Debugging

### **When Running the Worker**

1. **Enable Debug Logging**: Set logging level to DEBUG
2. **Monitor Extraction Logs**: Look for attribute extraction success/failure
3. **Check Parsing Results**: Verify parsed_result contains expected data
4. **Validate Transformation**: Confirm worker transformation preserves data
5. **Verify MongoDB Documents**: Check final document structure

### **Key Debug Commands**

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run worker with debug output
python -m worker.cli

# Check MongoDB documents
db.extraction_results.find().limit(5).pretty()
```

### **Expected Debug Output**

With the enhanced logging, you should see detailed output like:

```
🔍 _extract_attributes for extraction 0: type=<class 'langextract.Extraction'>
🔍 Extraction 0 object __dict__: {'product_attributes': {...}}
✅ Extraction 0: Using product_attributes format - {'base_product_name': '502 FS CBD Oil', ...}
✅ Extraction 0 final parsed_result: {'BaseProductName': '502 FS CBD Oil', 'Option1Name': 'Size', ...}
✅ Created ParsedResult: BaseProductName='502 FS CBD Oil' Option1Name='Size' ...
✅ Created document dict: {'parsed': {'BaseProductName': '502 FS CBD Oil', ...}}
```

## ✅ Resolution Strategy

The comprehensive debugging improvements will:

1. **Identify the exact point** where data is lost in the pipeline
2. **Show the actual structure** of LangExtract response objects
3. **Validate each transformation step** with detailed logging
4. **Confirm MongoDB document structure** before insertion
5. **Provide multiple fallback methods** for attribute extraction

**Result**: With these debug improvements, you'll be able to see exactly where the data transformation is failing and fix the specific issue causing empty/null values in MongoDB.

## 📋 Files Modified

1. **`langextract_core/extractor.py`**:
   - Enhanced `_parse_single_extraction` with step-by-step logging
   - Comprehensive `_extract_attributes` with 8 different extraction methods
   - Detailed object structure inspection and logging

2. **`worker/cli.py`**:
   - Enhanced `_transform_parsed_result` with input/output logging
   - Detailed MongoDB document creation logging
   - Validation of document structure before insertion

3. **`scripts/debug_data_transformation.py`**:
   - New comprehensive test script using exact debug data
   - Manual validation of parsing and transformation logic
   - Expected MongoDB document structure validation

The enhanced debugging will reveal exactly where the data is being lost and provide the information needed to fix the root cause of the empty/null values in MongoDB.
